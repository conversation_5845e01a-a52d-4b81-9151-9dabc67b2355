import { defineConfig } from '@umijs/max';
import { envConfig } from './src/Conf';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  locale: {
    default: 'zh-CN',
    antd: true,
    title: true,
    baseNavigator: false,
    baseSeparator: '-',
    useLocalStorage: true,
  },
  layout: {
    title: 'X-Pay 管理系统',
  },
  // Add favicon configuration
  favicons: ['/favicon.ico'],
  // Add links configuration for favicon
  links: [
    { rel: 'icon', href: '/favicon.ico', type: 'image/x-icon' },
    { rel: 'shortcut icon', href: '/favicon.ico', type: 'image/x-icon' },
  ],
  routes: [
    {
      path: '/',
      redirect: '/admin',
    },
    {
      path: '/callback',
      layout: false,
      component: './Callback',
    },
    {
      path: '/user/profile',
      component: './Admin/UserProfile',
    },
    {
      path: '/user/notice',
      component: './Admin/SystemNotice',
    },
    {
      path: '/admin',
      access: 'canAdmin',
      routes: [
        {
          path: '/admin',
          redirect: '/admin/dashboard',
        },
        {
          path: '/admin/dashboard',
          component: './Admin/Dashboard',
        },
        {
          path: '/admin/members',
          component: './Admin/MemberManagement',
        },
        {
          path: '/admin/users',
          component: './Admin/UserManagement',
        },
        {
          path: '/admin/deposit-records',
          component: './Admin/DepositRecordsManagement',
        },
        {
          path: '/admin/withdraw-records',
          component: './Admin/WithdrawRecordsManagement',
        },
        {
          path: '/admin/third-party-payment-orders',
          component: './Admin/ThirdPartyPaymentOrders',
        },
        {
          path: '/admin/merchant-transactions',
          component: './Admin/MerchantTransactionManagement',
        },
        {
          path: '/admin/callback-records',
          component: './Admin/CallbackRecordsManagement',
        },
        // {
        //   path: '/admin/posts',
        //   component: './Admin/PostManagement',
        // },
        // {
        //   path: '/admin/roles',
        //   component: './Admin/RoleManagement',
        // },
        {
          path: '/admin/menus',
          component: './Admin/MenuManagement',
        },
        //权限管理
        // {
        //   path: '/admin/permissions',
        //   component: './Admin/PermissionManagement',
        // },
        {
          path: '/admin/notice',
          component: './Admin/NoticeManagement',
        },
        {
          path: '/admin/exchange-products',
          component: './Admin/ExchangeProductManagement',
        },
        {
          path: '/admin/exchange-orders',
          component: './Admin/ExchangeOrderManagement',
        },
        {
          path: '/admin/exchange-swap-products',
          component: './Admin/ExchangeSwapProductManagement',
        },
        {
          path: '/admin/exchange-swap-products/create',
          component: './Admin/ExchangeSwapProductEdit',
        },
        {
          path: '/admin/exchange-swap-products/edit/:id',
          component: './Admin/ExchangeSwapProductEdit',
        },
        {
          path: '/admin/exchange-swap-orders',
          component: './Admin/ExchangeSwapOrderManagement',
        },
        {
          path: '/admin/exchange-swap-orders/detail/:id',
          component: './Admin/ExchangeSwapOrderDetail',
        },
        {
          path: '/admin/tokens',
          component: './Admin/TokenManagement',
        },
        {
          path: '/admin/transfers',
          component: './Admin/TransferManagement',
        },
        {
          path: '/admin/payment-requests',
          component: './Admin/PaymentRequestManagement',
        },
        {
          path: '/admin/red-packets',
          component: './Admin/RedPacketManagement',
        },
        {
          path: '/admin/red-envelope-records-list',
          component: './Admin/RedPacketClaimsManagement',
        },
        {
          path: '/admin/red-packet-images',
          component: './Admin/RedPacketImageManagement',
        },
        {
          path: '/admin/api-keys',
          component: './Admin/ApiKeyManagement',
        },
        {
          path: '/admin/login-log',
          component: './Admin/LoginLogManagement',
        },
        {
          path: '/admin/operation-log',
          component: './Admin/OperationLogManagement',
        },
        {
          path: '/admin/merchants',
          component: './Admin/MerchantManagement',
        },
        {
          path: '/admin/ip-access-list',
          component: './Admin/IpAccessListManagement',
        },
        { path: '/admin/config', component: './Admin/ConfigManagement' },
        {
          path: '/admin/permissions',
          component: './Admin/PermissionManagement',
        },
        { path: '/admin/agent/list', component: './Admin/AgentListManagement' },
        {
          path: '/admin/agent/create',
          component: './Admin/AgentCreateManagement',
        },
        {
          path: '/admin/merchant/list',
          component: './Admin/MerchantListManagement',
        },
        {
          path: '/admin/merchant/create',
          component: './Admin/MerchantCreateManagement',
        },
        {
          path: '/admin/merchant/assets',
          component: './Admin/MerchantAssetManagement',
        },
        {
          path: '/admin/users/backup',
          component: './Admin/UserBackupManagement',
        },
        {
          path: '/admin/fund/assets',
          component: './Admin/FundAssetsManagement',
        },
        {
          path: '/admin/fund/records',
          component: './Admin/FundRecordsManagement',
        },
        {
          path: '/admin/recharges',
          component: './Admin/FundRechargesManagement',
        },
        {
          path: '/admin/recharge-addresses',
          component: './Admin/FundRechargeAddressesManagement',
        },
        {
          path: '/admin/withdrawals',
          component: './Admin/FundWithdrawalsManagement',
        },
        {
          path: '/admin/withdrawal-config',
          component: './Admin/WithdrawalConfigManagement',
        },
      ],
    },
    // {
    //   path: '/agent',
    //   access: 'canAgent',
    //   routes: [
    //     {
    //       path: '/agent',
    //       redirect: '/agent/dashboard',
    //     },
    //     {
    //       path: '/agent/dashboard',
    //       component: './Agent/Dashboard',
    //     },
    //     {
    //       path: '/agent/merchants',
    //       component: './Agent/Merchants',
    //     },
    //     {
    //       path: '/agent/transactions',
    //       component: './Agent/Transactions',
    //     },
    //     {
    //       path: '/agent/settlements',
    //       component: './Agent/Settlements',
    //     },
    //     {
    //       path: '/agent/settings',
    //       component: './Agent/Settings',
    //     },
    //   ],
    // },
    // {
    //   path: '/merchant',
    //   access: 'canMerchant',
    //   routes: [
    //     {
    //       path: '/merchant',
    //       redirect: '/merchant/dashboard',
    //     },
    //     {
    //       path: '/merchant/dashboard',
    //       component: './Merchant/Dashboard',
    //     },
    //     {
    //       path: '/merchant/transactions',
    //       component: './Merchant/Transactions',
    //     },
    //     {
    //       path: '/merchant/settlements',
    //       component: './Merchant/Settlements',
    //     },
    //     {
    //       path: '/merchant/api',
    //       component: './Merchant/Api',
    //     },
    //     {
    //       path: '/merchant/settings',
    //       component: './Merchant/Settings',
    //     },
    //   ],
    // },
    // {
    //   path: '*',
    //   layout: false,
    //   component: './404',
    // },
  ],

  npmClient: 'pnpm',
  tailwindcss: {},
  proxy: {
    '/api': {
      target: envConfig.apiUrl,
      changeOrigin: true,
      // pathRewrite: { '^/api': '' },
      timeout: 30000,
      secure: false,
      onProxyReq: (proxyReq, req, res) => {
        //console.log('代理请求路径:', req.path);
        //console.log('实际请求路径:', proxyReq.path);
      },
      onProxyRes: (proxyRes) => {
        //console.log('代理响应状态:', proxyRes.statusCode);
      },
      onError: (err, req, res) => {
        console.error('代理错误:', err);
        res.writeHead(500, {
          'Content-Type': 'text/plain',
        });
        res.end(`代理请求错误: ${err.message}`);
      },
    },
  },
});
