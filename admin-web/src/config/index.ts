// Unified configuration management
import { envConfig } from '../Conf';
import { isValidConfig, validateConfig } from './schema';
import { AppConfig, ConfigSource } from './types';

class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig | null = null;
  private source: ConfigSource | null = null;
  private loadPromise: Promise<AppConfig> | null = null;

  private constructor() {}

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  async loadConfig(): Promise<AppConfig> {
    // Return existing config if already loaded
    if (this.config) {
      return this.config;
    }

    // Return existing promise if loading is in progress
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // Start loading
    this.loadPromise = this.loadConfigInternal();

    try {
      this.config = await this.loadPromise;
      return this.config;
    } finally {
      this.loadPromise = null;
    }
  }

  private async loadConfigInternal(): Promise<AppConfig> {
    try {
      // Try to load runtime configuration
      const response = await fetch('/runtime-config.json');

      if (response.ok) {
        const runtimeConfig = await response.json();

        // Validate runtime config
        const errors = validateConfig(runtimeConfig);
        if (errors.length > 0) {
          console.error('Runtime configuration validation errors:', errors);
          throw new Error('Invalid runtime configuration');
        }

        //console.log('Loaded runtime configuration successfully');
        this.source = ConfigSource.RUNTIME;
        return runtimeConfig as AppConfig;
      }
    } catch (error) {
      console.warn('Failed to load runtime configuration:', error);
    }

    // Fallback to build-time configuration
    //console.log('Using build-time configuration');
    this.source = ConfigSource.BUILDTIME;

    // Validate build-time config
    if (!isValidConfig(envConfig)) {
      throw new Error('Invalid build-time configuration');
    }

    return envConfig;
  }

  getConfig(): AppConfig | null {
    return this.config;
  }

  getConfigSource(): ConfigSource | null {
    return this.source;
  }

  // Reset config (useful for testing)
  reset(): void {
    this.config = null;
    this.source = null;
    this.loadPromise = null;
  }
}

// Export singleton instance
export const configManager = ConfigManager.getInstance();

// Convenience function
export async function getConfig(): Promise<AppConfig> {
  return configManager.loadConfig();
}
