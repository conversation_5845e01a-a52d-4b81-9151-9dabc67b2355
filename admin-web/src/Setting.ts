// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// Copyright 2023 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Sdk from 'casdoor-js-sdk';
import { getSystemAuth } from './api/interfaces/system-auth/system-auth';
import { envConfig } from './Conf';
import { configManager } from './config';

export const ServerUrl = 'http://localhost:8080';

// Initialize Casdoor SDK with build-time config as fallback
export let CasdoorSdk = new Sdk(envConfig);

// Async update Casdoor SDK with runtime config
configManager
  .loadConfig()
  .then((config) => {
    // Re-create SDK instance with runtime config
    CasdoorSdk = new Sdk(config);
    //console.log('Updated Casdoor SDK with config:', config);
  })
  .catch((error) => {
    console.warn(
      'Failed to load config for Casdoor SDK, using build-time config:',
      error,
    );
  });

export const isLoggedIn = () => {
  const token = localStorage.getItem('token');
  return token !== null && token.length > 0;
};

export const setToken = (token: string) => {
  localStorage.setItem('token', token);
};

export const goToLink = (link: string) => {
  window.location.href = link;
};

export const getSigninUrl = () => {
  return CasdoorSdk.getSigninUrl();
};

// 处理OAuth回调，获取token
export const handleOAuthCallback = async (code: string, state: string) => {
  if (code) {
    try {
      // 使用SDK的signin方法获取token
      const response: any =
        await getSystemAuth().postApiSystemAuthCasdoorSignin({
          code: code,
          state: state,
        });

      if (response.accessToken) {
        // 保存token到localStorage
        setToken(JSON.stringify(response.accessToken));

        return true;
      }

      throw new Error('获取token失败');
    } catch (error) {
      console.error('获取OAuth token失败:', error);
    }
  }
  return false;
};

// 获取Casdoor用户信息
export const getCasdoorUserInfo = async () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      return null;
    }

    const adminInfo = await getSystemAuth().getApiSystemAdminInfo();
    if (adminInfo) {
      // 直接检查返回的数据是否存在
      // 将用户信息存储到 localStorage，方便其他地方使用或持久化
      localStorage.setItem('userInfo', JSON.stringify(adminInfo));

      return adminInfo;
    } else {
      console.error('获取Casdoor用户信息失败');
      return null;
    }
  } catch (error) {
    console.error('获取Casdoor用户信息异常:', error);
    return null;
  }
};

export const handleCasdoorLogin = () => {
  const signinUrl = getSigninUrl();
  window.location.href = signinUrl;
};
