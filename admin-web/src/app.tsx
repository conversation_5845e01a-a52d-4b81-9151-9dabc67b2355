// 运行时配置

// 抑制第三方库的已知警告（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('./utils/suppressWarnings');
}

import { getSystemAuth } from '@/api/interfaces/system-auth/system-auth'; // 引入 API 函数
import type {
  XpayapiApiSystemV1GetAdminInfoRes,
  XpayapiApiSystemV1MenuDataItem,
} from '@/api/model'; // 引入类型
import * as Icons from '@ant-design/icons'; // 引入所有图标
import { history, RequestConfig } from '@umijs/max';
import React from 'react'; // 引入 React
import { AvatarDropdown } from './components/RightContent/AvatarDropdown';
import { GlobalWrapper } from './components/GlobalWrapper';
import { handleCasdoorLogin } from './Setting';

// 定义 initialState 类型
interface InitialState {
  currentUser?: XpayapiApiSystemV1GetAdminInfoRes;
  menuData?: XpayapiApiSystemV1MenuDataItem[];
  loading?: boolean;
}

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<InitialState> {
  // 如果是登录页面，不执行
  const { location } = history;
  if (location.pathname === '/login') {
    handleCasdoorLogin();
    return { loading: false };
  }

  if (location.pathname === '/callback') {
    return { loading: false };
  }

  // 获取用户信息和菜单
  const systemAuth = getSystemAuth();
  try {
    // 假设 getApiSystemAdminInfo 直接返回 XpayapiApiSystemV1GetAdminInfoRes 类型
    const adminInfo = await systemAuth.getApiSystemAdminInfo();
    if (adminInfo) {
      // 直接检查返回的数据是否存在
      // 将用户信息存储到 localStorage，方便其他地方使用或持久化
      localStorage.setItem('userInfo', JSON.stringify(adminInfo));
      return {
        currentUser: adminInfo, // 直接使用 adminInfo
        menuData: adminInfo.menus || [], // 使用后端返回的 menus
        loading: false,
      };
    } else {
      // API 调用成功但没有数据，或返回 null/undefined

      handleCasdoorLogin();
      return { loading: false };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 获取失败，重定向到登录页
    handleCasdoorLogin();
    return { loading: false };
  }
}

// --- 图标映射和菜单处理逻辑 ---
// 将图标字符串名称转换为 Ant Design 图标组件
const mapIcon = (iconName?: string): React.ReactNode | undefined => {
  if (!iconName) {
    return undefined;
  }
  // 将 iconName 转换为 PascalCase (e.g., dashboard -> Dashboard, red-envelope -> RedEnvelope)
  const pascalCaseName = iconName
    .split('-')
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
  const IconComponent =
    (Icons as any)[`${pascalCaseName}Outlined`] ||
    (Icons as any)[pascalCaseName]; // 尝试匹配 Outlined 后缀或直接匹配

  return IconComponent ? React.createElement(IconComponent) : undefined; // 返回图标组件实例或 undefined
};

// 递归处理菜单数据，将 icon 字符串替换为组件
const processMenuData = (menuData: XpayapiApiSystemV1MenuDataItem[]): any[] => {
  // 返回 any[] 以允许 icon 类型改变
  return menuData.map(
    (item) =>
      ({
        ...item,
        icon: mapIcon(item.icon), // 替换 icon
        children: item.children ? processMenuData(item.children) : undefined, // 递归处理子菜单
      } as any),
  ); // 使用类型断言允许 icon 类型从 string 变为 ReactNode
};
// --- 结束 ---

export const layout = ({
  initialState,
}: {
  initialState: InitialState | undefined;
}) => {
  const userInfo = localStorage.getItem('userInfo');
  const userInfoObj = userInfo ? JSON.parse(userInfo) : null;

  return {
    logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
    // 使用 initialState 中的数据渲染菜单
    menu: {
      locale: false, // 启用菜单国际化
    },
    locale: {
      default: 'zh-CN', // 默认语言
      antd: true, // 启用antd的国际化
      baseNavigator: false, // 关闭浏览器语言检测
    },
    // menuDataRender: () => initialState?.menuData || [], // 直接使用 initialState 中的 menuData
    menuDataRender: () =>
      processMenuData(initialState?.menuData || userInfoObj?.menus || []), // 使用处理函数转换图标
    layout: 'top', // 将布局改为顶部菜单模式
    splitMenus: true, // 在顶部模式下，splitMenus 通常无效或不需要，但保留也无妨
    actionsRender: () => {
      // AvatarDropdown 内部可以通过 useModel('@@initialState') 获取 currentUser
      // return [<NoticeIcon key="notice" />, <AvatarDropdown key="avatar" />];
      return [<AvatarDropdown key="avatar" />];
    },
    onPageChange: () => {
      const tokenInfo = localStorage.getItem('token');
      if (!tokenInfo) {
        handleCasdoorLogin();
      }
    },
    childrenRender: (children: React.ReactNode) => {
      // 使用 GlobalWrapper 包裹所有页面，以便显示全局通知
      return <GlobalWrapper>{children}</GlobalWrapper>;
    },
    // 其他配置保持不变...
  };
};

// 全局请求配置，可选，用于统一处理错误等
export const request: RequestConfig = {
  timeout: 10000,
  errorConfig: {
    errorHandler(error: any) {
      // 统一处理请求错误，例如 token 失效跳转登录页
      const { response } = error;
      if (response && response.status === 401) {
        localStorage.removeItem('token'); // 清除无效 token
        localStorage.removeItem('userInfo'); // 清除用户信息
        history.push('/login');
      }
      // 其他错误处理...
      console.error('Request error:', error);
    },
  },
  requestInterceptors: [
    // 请求拦截器，例如添加 token
    (config: any) => {
      const tokenInfo = localStorage.getItem('token');
      if (tokenInfo) {
        const tokenInfoObj = JSON.parse(tokenInfo);
        config.headers = {
          ...config.headers,
          Authorization: `${tokenInfoObj.token_type} ${tokenInfoObj.access_token}`,
        };
      }
      return config;
    },
  ],
};
