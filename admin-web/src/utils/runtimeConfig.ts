// Runtime configuration loader
// This allows the application to load configuration dynamically at runtime

interface RuntimeConfig {
  // API Configuration
  apiUrl: string;
  umiAppApiUrl: string;

  // Casdoor SDK Configuration
  serverUrl: string;
  clientId: string;
  organizationName: string;
  appName: string;
  redirectPath: string;
}

let runtimeConfig: RuntimeConfig | null = null;

/**
 * Load runtime configuration from the server
 * This function fetches the configuration that was generated by the entrypoint script
 */
export async function loadRuntimeConfig(): Promise<RuntimeConfig> {
  if (runtimeConfig) {
    return runtimeConfig;
  }

  try {
    // Try to fetch runtime configuration from the server
    const response = await fetch('/runtime-config.json');
    if (response.ok) {
      runtimeConfig = await response.json();
      //console.log('Loaded runtime configuration:', runtimeConfig);
      return runtimeConfig;
    }
  } catch (error) {
    console.warn(
      'Failed to load runtime configuration, falling back to build-time config:',
      error,
    );
  }

  // Fallback to build-time configuration
  const { envConfig } = await import('../Conf');
  runtimeConfig = envConfig;
  return runtimeConfig;
}

/**
 * Get the current runtime configuration
 * If not loaded yet, this will load it first
 */
export async function getRuntimeConfig(): Promise<RuntimeConfig> {
  if (!runtimeConfig) {
    return await loadRuntimeConfig();
  }
  return runtimeConfig;
}

/**
 * Reset the runtime configuration cache
 * Useful for testing or when configuration needs to be reloaded
 */
export function resetRuntimeConfig(): void {
  runtimeConfig = null;
}
