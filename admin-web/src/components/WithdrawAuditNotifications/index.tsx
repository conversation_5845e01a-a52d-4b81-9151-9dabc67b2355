import React, { useEffect, useState, useRef } from 'react';
import { message, notification } from 'antd';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { getSystemUserWithdraw } from '@/api/interfaces/system-user-withdraw/system-user-withdraw';
import type { AdminApiApiSystemV1PendingWithdrawInfo } from '@/api/model';
import { useTabCoordinator } from './useTabCoordinator';
import { AudioController } from './AudioController';
import './styles.less';

interface NotificationItem {
  id: string;
  withdraw: AdminApiApiSystemV1PendingWithdrawInfo;
  timestamp: string;
  isNew: boolean;
}

export const WithdrawAuditNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [isPolling, setIsPolling] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(false);
  const audioControllerRef = useRef<AudioController | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastWithdrawIdsRef = useRef<Set<number>>(new Set());
  const isFirstLoadRef = useRef(true);
  const isMountedRef = useRef(true);
  
  //console.log('WithdrawAuditNotifications component mounted');
  
  // 使用标签页协调器，确保只有一个标签页进行轮询
  const { isLeader, claimLeadership } = useTabCoordinator();

  // 调试notifications状态变化
  useEffect(() => {
    //console.log('Notifications state updated:', notifications);
    //console.log('Notifications length:', notifications.length);
  }, [notifications]);

  // 初始化音频控制器
  useEffect(() => {
    audioControllerRef.current = new AudioController('/assets/sounds/notification-alert.mp3');
    return () => {
      isMountedRef.current = false;
      audioControllerRef.current?.destroy();
    };
  }, []);

  // 获取待审核提现记录
  const fetchPendingAudits = async () => {
    //console.log('fetchPendingAudits called, isLeader:', isLeader);
    //console.log('Current isFirstLoadRef.current:', isFirstLoadRef.current);
    //console.log('Current notifications:', notifications);
    
    if (!isLeader) {
      //console.log('Not leader, skipping fetch');
      return;
    }

    try {
      const api = getSystemUserWithdraw();
      const response = await api.getApiSystemUserWithdrawsPendingAudits();
      
      //console.log('API response:', response);
      //console.log('API response type:', typeof response);
      //console.log('API response keys:', Object.keys(response));
      
      // 检查响应格式
      let withdraws = [];
      let count = 0;
      
      if (response && response.withdraws) {
        // 直接从response获取数据
        withdraws = response.withdraws || [];
        count = response.count || 0;
      } else if (response && response.data && response.data.data) {
        // 从response.data.data获取数据
        withdraws = response.data.data.withdraws || [];
        count = response.data.data.count || 0;
      } else if (response && response.data) {
        // 从response.data获取数据
        withdraws = response.data.withdraws || [];
        count = response.data.count || 0;
      }
        
        //console.log('Extracted withdraws:', withdraws);
        //console.log('Withdraws count:', count);
        //console.log('isFirstLoadRef.current:', isFirstLoadRef.current);
        
        // 第一次加载时，显示所有待审核记录
        if (isFirstLoadRef.current && withdraws.length > 0) {
          //console.log('First load with withdraws:', withdraws);
          isFirstLoadRef.current = false;
          
          const initialNotifications = withdraws.map((withdraw: AdminApiApiSystemV1PendingWithdrawInfo, index: number) => {
            //console.log(`Creating notification ${index} for withdraw:`, withdraw);
            return {
              id: `${withdraw.userWithdrawsId}_${Date.now()}_${index}`,
              withdraw,
              timestamp: new Date().toLocaleString('zh-CN'),
              isNew: true,
            };
          });
          
          //console.log('Setting initial notifications:', initialNotifications);
          
          // 确保组件仍然挂载
          if (isMountedRef.current) {
            setNotifications(initialNotifications);
            
            // 播放音频提醒
            if (audioControllerRef.current) {
              audioControllerRef.current.play().then(() => {
                setAudioEnabled(true);
              }).catch(() => {
                //console.log('Audio play requires user interaction');
              });
            }
            
            // 更新浏览器标题和favicon
            updateBrowserNotification(withdraws.length);
            
            // 记录当前的ID集合
            lastWithdrawIdsRef.current = new Set(withdraws.map((w: AdminApiApiSystemV1PendingWithdrawInfo) => w.userWithdrawsId || 0));
            //console.log('Initial notifications set, count:', initialNotifications.length);
            
            // 强制触发重新渲染
            setTimeout(() => {
              if (isMountedRef.current) {
                //console.log('Force re-render check after state update');
              }
            }, 100);
          }
        } else if (!isFirstLoadRef.current) {
          // 后续轮询，检查是否有新的待审核记录
          const currentIds = new Set(withdraws.map((w: AdminApiApiSystemV1PendingWithdrawInfo) => w.userWithdrawsId));
          const newWithdraws = withdraws.filter((w: AdminApiApiSystemV1PendingWithdrawInfo) => 
            !lastWithdrawIdsRef.current.has(w.userWithdrawsId || 0)
          );

          if (newWithdraws.length > 0) {
            // 创建新的通知
            const newNotifications = newWithdraws.map((withdraw: AdminApiApiSystemV1PendingWithdrawInfo) => ({
              id: `${withdraw.userWithdrawsId}_${Date.now()}`,
              withdraw,
              timestamp: new Date().toLocaleString('zh-CN'),
              isNew: true,
            }));

            setNotifications(prev => {
              //console.log('Adding new notifications to existing:', prev);
              return [...newNotifications, ...prev];
            });
            
            // 播放音频提醒
            if (audioControllerRef.current) {
              audioControllerRef.current.play().then(() => {
                setAudioEnabled(true);
              }).catch(() => {
                //console.log('Audio play requires user interaction');
              });
            }

            // 更新浏览器标题和favicon
            updateBrowserNotification(newWithdraws.length);
          }

          // 更新已知的提现ID集合
          lastWithdrawIdsRef.current = currentIds;
        }
    } catch (error) {
      console.error('获取待审核提现记录失败:', error);
      // 使用指数退避策略
      handlePollingError();
    }
  };

  // 处理轮询错误（指数退避）
  const handlePollingError = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      
      // 指数退避：1s -> 2s -> 4s -> 8s -> 16s (最大16秒)
      const retryDelay = Math.min(1000 * Math.pow(2, errorCountRef.current), 16000);
      errorCountRef.current++;
      
      setTimeout(() => {
        startPolling();
      }, retryDelay);
    }
  };

  const errorCountRef = useRef(0);

  // 开始轮询
  const startPolling = () => {
    if (!isLeader || pollingIntervalRef.current) return;

    setIsPolling(true);
    errorCountRef.current = 0; // 重置错误计数
    
    // 立即执行一次
    fetchPendingAudits();
    
    // 设置2秒轮询间隔
    pollingIntervalRef.current = setInterval(() => {
      fetchPendingAudits();
    }, 2000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  };

  // 关闭通知
  const closeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
    
    // 如果所有通知都关闭了，停止音频
    if (notifications.length === 1) {
      audioControllerRef.current?.stop();
      resetBrowserNotification();
    }
  };

  // 关闭所有通知
  const closeAllNotifications = () => {
    setNotifications([]);
    audioControllerRef.current?.stop();
    resetBrowserNotification();
  };

  // 更新浏览器通知
  const updateBrowserNotification = (count: number) => {
    // 更新标题
    document.title = `(${count}) 新的待审核提现 - XPay Admin`;
    
    // 更新favicon（如果支持）
    const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement;
    if (link) {
      link.href = '/favicon-alert.ico'; // 需要准备一个提醒版的favicon
    }
  };

  // 重置浏览器通知
  const resetBrowserNotification = () => {
    document.title = 'XPay Admin';
    
    const link = document.querySelector("link[rel*='icon']") as HTMLLinkElement;
    if (link) {
      link.href = '/favicon.ico';
    }
  };

  // 监听标签页leadership变化
  useEffect(() => {
    //console.log('Leadership changed, isLeader:', isLeader);
    if (isLeader) {
      // 成为leader时重置首次加载标志
      isFirstLoadRef.current = true;
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [isLeader]);

  // 页面卸载时清理
  useEffect(() => {
    const handleUnload = () => {
      stopPolling();
    };

    window.addEventListener('beforeunload', handleUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
      stopPolling();
    };
  }, []);

  //console.log('Component render called');
  //console.log('Current notifications:', notifications);
  //console.log('Notifications length:', notifications.length);
  
  if (notifications.length === 0) {
    //console.log('No notifications to display, returning null');
    return null;
  }
  
  //console.log('Rendering notifications UI');

  return (
    <div className="withdraw-audit-notifications">
      <div className="notifications-header">
        <h3>待审核提现通知 ({notifications.length})</h3>
        <div className="header-actions">
          {!audioEnabled && (
            <button 
              className="audio-enable-btn"
              onClick={() => {
                if (audioControllerRef.current) {
                  audioControllerRef.current.play().then(() => {
                    setAudioEnabled(true);
                  });
                }
              }}
              title="启用声音提醒"
            >
              🔊
            </button>
          )}
          <button className="close-all-btn" onClick={closeAllNotifications}>
            关闭全部
          </button>
        </div>
      </div>
      
      <div className="notifications-list">
        {notifications.map(({ id, withdraw, timestamp }) => (
          <div key={id} className="notification-item">
            <div className="notification-header">
              <ExclamationCircleOutlined className="notification-icon" />
              <button 
                className="notification-close"
                onClick={() => closeNotification(id)}
              >
                <CloseOutlined />
              </button>
            </div>
            
            <div className="notification-content">
              <div className="notification-message">
                您有等待审核的提现申请，请您及时处理！
              </div>
              
              <div className="notification-details">
                <div className="detail-row">
                  <span className="label">用户:</span>
                  <span className="value">{withdraw.account || ''} ({withdraw.nickname || ''})</span>
                </div>
                <div className="detail-row">
                  <span className="label">金额:</span>
                  <span className="value">{withdraw.amount || 0} {withdraw.symbol || ''}</span>
                </div>
                <div className="detail-row">
                  <span className="label">订单号:</span>
                  <span className="value">{withdraw.orderNo || ''}</span>
                </div>
                <div className="detail-row">
                  <span className="label">等待时间:</span>
                  <span className="value waiting-time">{withdraw.waitingTime || ''}</span>
                </div>
              </div>
              
              <div className="notification-footer">
                <span className="timestamp">{timestamp}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};