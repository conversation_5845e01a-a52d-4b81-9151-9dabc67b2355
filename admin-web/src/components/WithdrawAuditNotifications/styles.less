// Use CSS variables instead of importing antd theme
@primary-color: #1890ff;
@warning-color: #faad14;
@error-color: #f5222d;

.withdraw-audit-notifications {
  position: fixed;
  right: 20px;
  top: 80px;
  width: 420px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  z-index: 1050;
  filter: drop-shadow(0 4px 24px rgba(0, 0, 0, 0.12));
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  .notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 20px;
    background: linear-gradient(135deg, @primary-color 0%, darken(@primary-color, 5%) 100%);
    color: #fff;
    border-radius: 12px 12px 0 0;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
    }

    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .audio-enable-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: #fff;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: pulse 2s infinite;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }
    }

    .close-all-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: #fff;
      padding: 4px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .notifications-list {
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 600px;
    overflow-y: auto;
  }

  .notification-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    animation: slideInRight 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    transition: all 0.2s ease;
    
    &:last-child {
      border-bottom: none;
      border-radius: 0 0 12px 12px;
    }

    &:hover {
      background: #f8faff;
      transform: translateX(-2px);
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px 8px;

      .notification-icon {
        font-size: 22px;
        color: @warning-color;
        animation: pulse 2s infinite;
        filter: drop-shadow(0 0 8px rgba(250, 173, 20, 0.3));
      }

      .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        color: #999;
        transition: all 0.3s;
        border-radius: 4px;

        &:hover {
          background: #f0f0f0;
          color: #333;
        }
      }
    }

    .notification-content {
      padding: 0 16px 12px;

      .notification-message {
        font-size: 15px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 16px;
        line-height: 1.6;
        letter-spacing: 0.3px;
      }

      .notification-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          font-size: 14px;
          padding: 4px 8px;
          border-radius: 6px;
          transition: background 0.2s ease;
          
          &:hover {
            background: rgba(24, 144, 255, 0.04);
          }

          .label {
            color: #8c8c8c;
            font-weight: 400;
          }

          .value {
            color: #1a1a1a;
            font-weight: 600;
            max-width: 60%;
            text-align: right;
            word-break: break-word;
            
            &.waiting-time {
              color: @error-color;
              font-weight: 700;
              padding: 2px 8px;
              background: rgba(245, 34, 45, 0.08);
              border-radius: 4px;
            }
          }
        }
      }

      .notification-footer {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        .timestamp {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

// 动画效果
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// 音频权限提示样式
.audio-permission-prompt {
  position: fixed;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, @warning-color 0%, darken(@warning-color, 10%) 100%);
  color: #fff;
  padding: 20px 32px;
  border-radius: 12px;
  z-index: 2000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: slideDown 0.4s cubic-bezier(0.23, 1, 0.320, 1);

  .prompt-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .prompt-icon {
      font-size: 28px;
      animation: pulse 1.5s infinite;
    }

    .prompt-text {
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid @warning-color;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .withdraw-audit-notifications {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}