export class AudioController {
  private audio: HTMLAudioElement;
  private isPlaying: boolean = false;
  private playPromise: Promise<void> | null = null;

  constructor(audioUrl: string) {
    this.audio = new Audio(audioUrl);
    this.audio.loop = true; // 设置循环播放
    this.audio.volume = 0.5; // 设置默认音量为50%
    
    // 预加载音频
    this.audio.load();
  }

  /**
   * 播放音频提醒（连续循环）
   */
  async play(): Promise<void> {
    if (this.isPlaying) return;

    try {
      // 处理浏览器的自动播放策略
      this.playPromise = this.audio.play();
      await this.playPromise;
      this.isPlaying = true;
      //console.log('Audio notification started');
    } catch (error) {
      // 自动播放被阻止，需要用户交互
      console.warn('Audio autoplay blocked. User interaction required.', error);
      
      // 显示一个提示，要求用户点击启用声音
      this.showAudioPermissionPrompt();
    }
  }

  /**
   * 停止音频播放
   */
  stop(): void {
    if (!this.isPlaying) return;

    // 等待play promise完成后再停止
    if (this.playPromise) {
      this.playPromise.then(() => {
        this.audio.pause();
        this.audio.currentTime = 0;
        this.isPlaying = false;
        //console.log('Audio notification stopped');
      }).catch(() => {
        // 播放已经被中断，直接重置状态
        this.isPlaying = false;
      });
    } else {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.isPlaying = false;
    }
  }

  /**
   * 设置音量
   * @param volume 0-1之间的数值
   */
  setVolume(volume: number): void {
    this.audio.volume = Math.max(0, Math.min(1, volume));
  }

  /**
   * 获取当前音量
   */
  getVolume(): number {
    return this.audio.volume;
  }

  /**
   * 检查是否正在播放
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * 显示音频权限提示
   */
  private showAudioPermissionPrompt(): void {
    // 创建一个临时的点击处理器
    const enableAudio = () => {
      this.play().then(() => {
        // 播放成功，移除监听器
        document.removeEventListener('click', enableAudio, { capture: true });
      });
    };

    // 添加一次性的点击监听器
    document.addEventListener('click', enableAudio, { capture: true, once: true });

    // 显示提示消息
    const message = document.createElement('div');
    message.className = 'audio-permission-prompt';
    message.innerHTML = `
      <div class="prompt-content">
        <span class="prompt-icon">🔊</span>
        <span class="prompt-text">点击页面任意位置启用声音提醒</span>
      </div>
    `;
    
    document.body.appendChild(message);

    // 3秒后自动移除提示
    setTimeout(() => {
      if (document.body.contains(message)) {
        message.remove();
      }
    }, 3000);

    // 监听音频播放成功事件，立即移除提示
    this.audio.addEventListener('play', () => {
      if (document.body.contains(message)) {
        message.remove();
      }
    }, { once: true });
  }

  /**
   * 销毁音频控制器
   */
  destroy(): void {
    this.stop();
    this.audio.remove();
  }
}