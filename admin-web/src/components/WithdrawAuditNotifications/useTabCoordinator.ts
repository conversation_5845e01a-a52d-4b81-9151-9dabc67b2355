import { useEffect, useState, useRef, useCallback } from 'react';

const TAB_COORDINATOR_KEY = 'withdraw_audit_tab_coordinator';
const HEARTBEAT_INTERVAL = 500; // 500ms心跳间隔
const LEADER_TIMEOUT = 1500; // 1.5秒没有心跳就认为leader失效

interface TabInfo {
  tabId: string;
  lastHeartbeat: number;
  isLeader: boolean;
}

export const useTabCoordinator = () => {
  const [isLeader, setIsLeader] = useState(false);
  const tabIdRef = useRef(`tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const broadcastChannelRef = useRef<BroadcastChannel | null>(null);

  // 使用BroadcastChannel进行标签页通信
  const initBroadcastChannel = useCallback(() => {
    if ('BroadcastChannel' in window) {
      broadcastChannelRef.current = new BroadcastChannel(TAB_COORDINATOR_KEY);
      
      broadcastChannelRef.current.onmessage = (event) => {
        const { type, tabId, timestamp } = event.data;
        
        switch (type) {
          case 'heartbeat':
            if (tabId !== tabIdRef.current && isLeader) {
              // 如果收到其他标签页的心跳且自己是leader，检查是否需要让位
              checkLeadershipConflict(tabId, timestamp);
            }
            break;
            
          case 'claim_leadership':
            if (isLeader && tabId !== tabIdRef.current) {
              // 其他标签页声称leadership，让位
              setIsLeader(false);
            }
            break;
            
          case 'release_leadership':
            if (!isLeader && tabId !== tabIdRef.current) {
              // leader释放了leadership，尝试接管
              setTimeout(() => attemptLeadership(), 100);
            }
            break;
        }
      };
    }
  }, [isLeader]);

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    if (!isLeader) return;

    const heartbeatData: TabInfo = {
      tabId: tabIdRef.current,
      lastHeartbeat: Date.now(),
      isLeader: true,
    };

    // 通过BroadcastChannel发送心跳
    if (broadcastChannelRef.current) {
      broadcastChannelRef.current.postMessage({
        type: 'heartbeat',
        tabId: tabIdRef.current,
        timestamp: Date.now(),
      });
    }

    // 同时更新localStorage（作为fallback）
    try {
      localStorage.setItem(TAB_COORDINATOR_KEY, JSON.stringify(heartbeatData));
    } catch (e) {
      console.warn('Failed to update localStorage:', e);
    }
  }, [isLeader]);

  // 检查当前leader是否仍然活跃
  const checkCurrentLeader = useCallback(() => {
    try {
      const storedData = localStorage.getItem(TAB_COORDINATOR_KEY);
      if (!storedData) return null;

      const leaderInfo: TabInfo = JSON.parse(storedData);
      const now = Date.now();

      // 检查leader是否超时
      if (now - leaderInfo.lastHeartbeat > LEADER_TIMEOUT) {
        return null; // Leader已失效
      }

      return leaderInfo;
    } catch (e) {
      console.warn('Failed to check current leader:', e);
      return null;
    }
  }, []);

  // 尝试成为leader
  const attemptLeadership = useCallback(() => {
    const currentLeader = checkCurrentLeader();
    
    if (!currentLeader) {
      // 没有活跃的leader，尝试成为leader
      setIsLeader(true);
      
      // 广播声明leadership
      if (broadcastChannelRef.current) {
        broadcastChannelRef.current.postMessage({
          type: 'claim_leadership',
          tabId: tabIdRef.current,
          timestamp: Date.now(),
        });
      }
      
      // 立即发送心跳
      sendHeartbeat();
      
      //console.log(`Tab ${tabIdRef.current} became leader`);
    }
  }, [checkCurrentLeader, sendHeartbeat]);

  // 处理leadership冲突
  const checkLeadershipConflict = useCallback((otherTabId: string, timestamp: number) => {
    // 使用tabId作为tie-breaker（字典序较小的优先）
    if (otherTabId < tabIdRef.current) {
      setIsLeader(false);
      //console.log(`Tab ${tabIdRef.current} yielded leadership to ${otherTabId}`);
    }
  }, []);

  // 主动声明leadership
  const claimLeadership = useCallback(() => {
    attemptLeadership();
  }, [attemptLeadership]);

  // 释放leadership
  const releaseLeadership = useCallback(() => {
    if (!isLeader) return;

    setIsLeader(false);
    
    // 广播释放leadership
    if (broadcastChannelRef.current) {
      broadcastChannelRef.current.postMessage({
        type: 'release_leadership',
        tabId: tabIdRef.current,
        timestamp: Date.now(),
      });
    }
    
    // 清除localStorage
    try {
      localStorage.removeItem(TAB_COORDINATOR_KEY);
    } catch (e) {
      console.warn('Failed to clear localStorage:', e);
    }
  }, [isLeader]);

  // 初始化
  useEffect(() => {
    initBroadcastChannel();
    
    // 延迟一下再尝试成为leader，让其他标签页有机会先声明
    setTimeout(() => attemptLeadership(), 500);

    // 设置心跳定时器
    heartbeatIntervalRef.current = setInterval(() => {
      if (isLeader) {
        sendHeartbeat();
      } else {
        // 非leader定期检查是否需要接管
        attemptLeadership();
      }
    }, HEARTBEAT_INTERVAL);

    // 监听页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isLeader) {
        // 页面变为可见时，尝试成为leader
        setTimeout(() => attemptLeadership(), 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      // 停止心跳
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }

      // 释放leadership
      releaseLeadership();

      // 关闭BroadcastChannel
      if (broadcastChannelRef.current) {
        broadcastChannelRef.current.close();
      }

      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 当leadership状态改变时更新心跳
  useEffect(() => {
    if (isLeader) {
      sendHeartbeat();
    }
  }, [isLeader, sendHeartbeat]);

  return {
    isLeader,
    tabId: tabIdRef.current,
    claimLeadership,
    releaseLeadership,
  };
};