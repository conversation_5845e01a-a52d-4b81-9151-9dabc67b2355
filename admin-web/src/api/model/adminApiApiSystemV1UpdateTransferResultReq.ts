/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateTransferResultReqResult } from './adminApiApiSystemV1UpdateTransferResultReqResult';

export interface AdminApiApiSystemV1UpdateTransferResultReq {
  /** 提现记录ID */
  withdrawId?: number;
  /** 转账结果: success-成功, failed-失败 */
  result: AdminApiApiSystemV1UpdateTransferResultReqResult;
  /** 交易哈希 */
  txHash?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 管理员备注 */
  adminRemark?: string;
}
