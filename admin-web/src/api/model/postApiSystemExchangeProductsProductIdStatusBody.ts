/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PostApiSystemExchangeProductsProductIdStatusBodyStatus } from './postApiSystemExchangeProductsProductIdStatusBodyStatus';
import type { PostApiSystemExchangeProductsProductIdStatusBodyIsActive } from './postApiSystemExchangeProductsProductIdStatusBodyIsActive';

export type PostApiSystemExchangeProductsProductIdStatusBody = {
  /** 状态 */
  status: PostApiSystemExchangeProductsProductIdStatusBodyStatus;
  /** 是否激活 */
  isActive: PostApiSystemExchangeProductsProductIdStatusBodyIsActive;
};
