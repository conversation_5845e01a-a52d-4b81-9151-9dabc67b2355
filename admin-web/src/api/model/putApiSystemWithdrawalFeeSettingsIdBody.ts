/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemWithdrawalFeeSettingsIdBodyFeeType } from './putApiSystemWithdrawalFeeSettingsIdBodyFeeType';
import type { PutApiSystemWithdrawalFeeSettingsIdBodyStatus } from './putApiSystemWithdrawalFeeSettingsIdBodyStatus';

export type PutApiSystemWithdrawalFeeSettingsIdBody = {
  amountMax: string;
  feeType: PutApiSystemWithdrawalFeeSettingsIdBodyFeeType;
  feeValue: string;
  status?: PutApiSystemWithdrawalFeeSettingsIdBodyStatus;
  currency: string;
  network: string;
  amountMin: string;
};
