/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemMenusIdBodyStatus } from './putApiSystemMenusIdBodyStatus';
import type { PutApiSystemMenusIdBodyHideInMenu } from './putApiSystemMenusIdBodyHideInMenu';
import type { PutApiSystemMenusIdBodyHideChildrenInMenu } from './putApiSystemMenusIdBodyHideChildrenInMenu';

export type PutApiSystemMenusIdBody = {
  /** 父菜单ID (0为根目录) */
  pid: number;
  /**
   * 路由路径 (目录/菜单需要)
   * @maxLength 200
   */
  path?: string;
  /**
   * 跳转目标 (如：_blank, _self等)
   * @maxLength 50
   */
  target?: string;
  /**
   * 菜单唯一标识 (建议唯一，英文)
   * @maxLength 50
   */
  key?: string;
  /**
   * 菜单图标
   * @maxLength 50
   */
  icon?: string;
  /**
   * 排序
   * @minimum 0
   */
  sort: number;
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
  /** 菜单状态(0:禁用, 1:启用) */
  status: PutApiSystemMenusIdBodyStatus;
  /**
   * 菜单名称 (建议唯一，支持中英文)
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** 是否在菜单中隐藏 (0: 显示, 1: 隐藏) */
  hideInMenu?: PutApiSystemMenusIdBodyHideInMenu;
  /** 是否在子菜单中隐藏 (0: 显示, 1: 隐藏) */
  hideChildrenInMenu?: PutApiSystemMenusIdBodyHideChildrenInMenu;
  /**
   * 权限标识 (如：admin:menu:add等)
   * @maxLength 50
   */
  access?: string;
};
