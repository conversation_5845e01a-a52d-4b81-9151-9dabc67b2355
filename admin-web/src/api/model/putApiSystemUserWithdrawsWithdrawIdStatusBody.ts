/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemUserWithdrawsWithdrawIdStatusBodyAuditStatus } from './putApiSystemUserWithdrawsWithdrawIdStatusBodyAuditStatus';
import type { PutApiSystemUserWithdrawsWithdrawIdStatusBodyProcessingStatus } from './putApiSystemUserWithdrawsWithdrawIdStatusBodyProcessingStatus';

export type PutApiSystemUserWithdrawsWithdrawIdStatusBody = {
  /** 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝 */
  auditStatus: PutApiSystemUserWithdrawsWithdrawIdStatusBodyAuditStatus;
  /** 处理状态: 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败 */
  processingStatus: PutApiSystemUserWithdrawsWithdrawIdStatusBodyProcessingStatus;
  /** 交易哈希 */
  txHash?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 管理员备注 */
  adminRemark?: string;
};
