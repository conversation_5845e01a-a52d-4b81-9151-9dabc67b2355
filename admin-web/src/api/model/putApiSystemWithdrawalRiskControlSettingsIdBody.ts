/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemWithdrawalRiskControlSettingsIdBodyStatus } from './putApiSystemWithdrawalRiskControlSettingsIdBodyStatus';
import type { PutApiSystemWithdrawalRiskControlSettingsIdBodyControlType } from './putApiSystemWithdrawalRiskControlSettingsIdBodyControlType';
import type { PutApiSystemWithdrawalRiskControlSettingsIdBodyTimeUnit } from './putApiSystemWithdrawalRiskControlSettingsIdBodyTimeUnit';

export type PutApiSystemWithdrawalRiskControlSettingsIdBody = {
  maxAmount?: string;
  status?: PutApiSystemWithdrawalRiskControlSettingsIdBodyStatus;
  controlType: PutApiSystemWithdrawalRiskControlSettingsIdBodyControlType;
  /** @minimum 1 */
  timePeriod: number;
  timeUnit: PutApiSystemWithdrawalRiskControlSettingsIdBodyTimeUnit;
  maxTimes?: number;
};
