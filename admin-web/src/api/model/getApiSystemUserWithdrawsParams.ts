/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemUserWithdrawsAuditStatus } from './getApiSystemUserWithdrawsAuditStatus';
import type { GetApiSystemUserWithdrawsProcessingStatus } from './getApiSystemUserWithdrawsProcessingStatus';
import type { GetApiSystemUserWithdrawsFiatType } from './getApiSystemUserWithdrawsFiatType';

export type GetApiSystemUserWithdrawsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 用户ID
 */
userId?: number;
/**
 * 用户账号
 */
account?: string;
/**
 * 用户名
 */
username?: string;
/**
 * 币种ID
 */
tokenId?: number;
/**
 * 币种符号
 */
symbol?: string;
/**
 * 链名称
 */
chain?: string;
/**
 * 提币地址
 */
address?: string;
/**
 * 订单号
 */
orderNo?: string;
/**
 * 审核状态: 0-全部, 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
 */
auditStatus?: GetApiSystemUserWithdrawsAuditStatus;
/**
 * 处理状态: 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败
 */
processingStatus?: GetApiSystemUserWithdrawsProcessingStatus;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 最小金额
 */
amountMin?: number;
/**
 * 最大金额
 */
amountMax?: number;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 名字 (模糊搜索)
 */
firstName?: string;
/**
 * 法币提现类型: alipay_account-支付宝账号, alipay_qr-支付宝二维码, wechat_qr-微信二维码
 */
fiatType?: GetApiSystemUserWithdrawsFiatType;
/**
 * 法币收款人姓名 (模糊搜索)
 */
recipientName?: string;
/**
 * 法币收款账户 (模糊搜索)
 */
recipientAccount?: string;
};
