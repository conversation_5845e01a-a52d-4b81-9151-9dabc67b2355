/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ApproveUserWithdrawRes,
  AdminApiApiSystemV1GetPendingAuditsRes,
  AdminApiApiSystemV1GetUserWithdrawDetailRes,
  AdminApiApiSystemV1ListUserWithdrawsRes,
  AdminApiApiSystemV1RejectUserWithdrawRes,
  AdminApiApiSystemV1ReviewUserWithdrawRes,
  AdminApiApiSystemV1UpdateTransferResultRes,
  AdminApiApiSystemV1UpdateUserWithdrawStatusRes,
  GetApiSystemUserWithdrawsParams,
  PostApiSystemUserWithdrawsWithdrawIdApproveBody,
  PostApiSystemUserWithdrawsWithdrawIdRejectBody,
  PutApiSystemUserWithdrawsWithdrawIdReviewBody,
  PutApiSystemUserWithdrawsWithdrawIdStatusBody,
  PutApiSystemUserWithdrawsWithdrawIdTransferResultBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUserWithdraw = () => {
/**
 * @summary 获取提现记录列表
 */
const getApiSystemUserWithdraws = (
    params: GetApiSystemUserWithdrawsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListUserWithdrawsRes>(
      {url: `/api/system/user-withdraws`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取待审核提现记录
 */
const getApiSystemUserWithdrawsPendingAudits = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetPendingAuditsRes>(
      {url: `/api/system/user-withdraws/pending-audits`, method: 'GET'
    },
      );
    }
  /**
 * @summary 获取提现记录详情
 */
const getApiSystemUserWithdrawsWithdrawId = (
    withdrawId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserWithdrawDetailRes>(
      {url: `/api/system/user-withdraws/${withdrawId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 审批提现申请
 */
const postApiSystemUserWithdrawsWithdrawIdApprove = (
    withdrawId: number,
    postApiSystemUserWithdrawsWithdrawIdApproveBody: PostApiSystemUserWithdrawsWithdrawIdApproveBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ApproveUserWithdrawRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/approve`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemUserWithdrawsWithdrawIdApproveBody
    },
      );
    }
  /**
 * @summary 拒绝提现申请
 */
const postApiSystemUserWithdrawsWithdrawIdReject = (
    withdrawId: number,
    postApiSystemUserWithdrawsWithdrawIdRejectBody: PostApiSystemUserWithdrawsWithdrawIdRejectBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1RejectUserWithdrawRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/reject`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemUserWithdrawsWithdrawIdRejectBody
    },
      );
    }
  /**
 * @summary 审核提现记录
 */
const putApiSystemUserWithdrawsWithdrawIdReview = (
    withdrawId: number,
    putApiSystemUserWithdrawsWithdrawIdReviewBody: PutApiSystemUserWithdrawsWithdrawIdReviewBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ReviewUserWithdrawRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/review`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUserWithdrawsWithdrawIdReviewBody
    },
      );
    }
  /**
 * @summary 更新提现记录状态
 */
const putApiSystemUserWithdrawsWithdrawIdStatus = (
    withdrawId: number,
    putApiSystemUserWithdrawsWithdrawIdStatusBody: PutApiSystemUserWithdrawsWithdrawIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateUserWithdrawStatusRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUserWithdrawsWithdrawIdStatusBody
    },
      );
    }
  /**
 * @summary 更新转账结果
 */
const putApiSystemUserWithdrawsWithdrawIdTransferResult = (
    withdrawId: number,
    putApiSystemUserWithdrawsWithdrawIdTransferResultBody: PutApiSystemUserWithdrawsWithdrawIdTransferResultBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateTransferResultRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/transfer-result`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUserWithdrawsWithdrawIdTransferResultBody
    },
      );
    }
  return {getApiSystemUserWithdraws,getApiSystemUserWithdrawsPendingAudits,getApiSystemUserWithdrawsWithdrawId,postApiSystemUserWithdrawsWithdrawIdApprove,postApiSystemUserWithdrawsWithdrawIdReject,putApiSystemUserWithdrawsWithdrawIdReview,putApiSystemUserWithdrawsWithdrawIdStatus,putApiSystemUserWithdrawsWithdrawIdTransferResult}};
export type GetApiSystemUserWithdrawsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['getApiSystemUserWithdraws']>>>
export type GetApiSystemUserWithdrawsPendingAuditsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['getApiSystemUserWithdrawsPendingAudits']>>>
export type GetApiSystemUserWithdrawsWithdrawIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['getApiSystemUserWithdrawsWithdrawId']>>>
export type PostApiSystemUserWithdrawsWithdrawIdApproveResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['postApiSystemUserWithdrawsWithdrawIdApprove']>>>
export type PostApiSystemUserWithdrawsWithdrawIdRejectResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['postApiSystemUserWithdrawsWithdrawIdReject']>>>
export type PutApiSystemUserWithdrawsWithdrawIdReviewResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['putApiSystemUserWithdrawsWithdrawIdReview']>>>
export type PutApiSystemUserWithdrawsWithdrawIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['putApiSystemUserWithdrawsWithdrawIdStatus']>>>
export type PutApiSystemUserWithdrawsWithdrawIdTransferResultResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['putApiSystemUserWithdrawsWithdrawIdTransferResult']>>>
