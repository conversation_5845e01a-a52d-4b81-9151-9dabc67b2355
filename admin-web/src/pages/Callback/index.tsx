import { getSystemAuth } from '@/api/interfaces/system-auth/system-auth';
import { handleCasdoorLogin, handleOAuthCallback } from '@/Setting';
import { history } from '@umijs/max';
import { Button, Result, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

/**
 * OAuth回调处理组件
 * 处理从Casdoor认证服务器重定向回来的请求
 */
const Callback: React.FC = () => {
  const [_loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        // 从URL中获取授权码
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');

        if (!code || !state) {
          setError('未收到授权码，认证失败');
          setLoading(false);
          return;
        }
        // 使用授权码获取token
        const success = await handleOAuthCallback(code, state);
        if (!success) {
          setError('获取访问令牌失败');
          setLoading(false);
          return;
        }

        // 获取用户信息
        const userInfo = await getSystemAuth().getApiSystemAdminInfo();
        if (!userInfo) {
          setError('获取用户信息失败');
          setLoading(false);
          return;
        }

        // 将用户信息保存到全局状态
        localStorage.setItem('userInfo', JSON.stringify(userInfo));

        history.replace('/admin/dashboard');
      } catch (error) {
        console.error('回调处理失败:', error);
        setError('认证过程中发生错误，请重试');
        setLoading(false);
      }
    };

    processCallback();
  }, []);

  if (error) {
    return (
      <Result
        status="error"
        title="登录失败"
        subTitle={error}
        extra={[
          <Button
            type="primary"
            key="login"
            onClick={() => handleCasdoorLogin()}
          >
            返回登录
          </Button>,
        ]}
      />
    );
  }
  //console.log('callback page');
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <Spin size="large" tip="登录中，请稍候..." />
    </div>
  );
};

export default Callback;
