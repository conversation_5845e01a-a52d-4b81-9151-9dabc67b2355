import {
  BankOutlined,
  SaveOutlined,
  UploadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Avatar,
  Button,
  Card,
  Form,
  Input,
  Select,
  Switch,
  Tabs,
  Upload,
  message,
} from 'antd';
import React from 'react';

const { TabPane } = Tabs;
const { Option } = Select;

const MerchantSettings: React.FC = () => {
  const [profileForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  const onProfileFinish = (values: any) => {
    //console.log('Profile settings:', values);
    message.success('Profile settings saved successfully');
  };

  const onPaymentFinish = (values: any) => {
    //console.log('Payment settings:', values);
    message.success('Payment settings saved successfully');
  };

  const onSecurityFinish = (values: any) => {
    //console.log('Security settings:', values);
    message.success('Security settings saved successfully');
  };

  const onNotificationFinish = (values: any) => {
    //console.log('Notification settings:', values);
    message.success('Notification settings saved successfully');
  };

  return (
    <PageContainer title="Merchant Settings">
      <Card>
        <Tabs defaultActiveKey="profile">
          <TabPane tab="Business Profile" key="profile">
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                businessName: 'My Business',
                contactName: 'John Smith',
                email: '<EMAIL>',
                phone: '+****************',
                website: 'www.mybusiness.com',
                address: '123 Main St, Anytown, USA',
                businessType: 'retail',
                description: 'Local retail business selling quality products.',
              }}
              onFinish={onProfileFinish}
            >
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Avatar size={100} icon={<UserOutlined />} />
                <div style={{ marginTop: 16 }}>
                  <Upload>
                    <Button icon={<UploadOutlined />}>Change Logo</Button>
                  </Upload>
                </div>
              </div>

              <Form.Item
                label="Business Name"
                name="businessName"
                rules={[
                  {
                    required: true,
                    message: 'Please input your business name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Contact Person"
                name="contactName"
                rules={[
                  {
                    required: true,
                    message: 'Please input contact person name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Email"
                name="email"
                rules={[
                  { required: true, message: 'Please input your email!' },
                  { type: 'email', message: 'Please enter a valid email!' },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item label="Phone Number" name="phone">
                <Input />
              </Form.Item>

              <Form.Item label="Website" name="website">
                <Input />
              </Form.Item>

              <Form.Item label="Business Address" name="address">
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item label="Business Type" name="businessType">
                <Select>
                  <Option value="retail">Retail</Option>
                  <Option value="restaurant">Restaurant</Option>
                  <Option value="service">Service</Option>
                  <Option value="ecommerce">E-Commerce</Option>
                  <Option value="other">Other</Option>
                </Select>
              </Form.Item>

              <Form.Item label="Business Description" name="description">
                <Input.TextArea rows={4} />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Save Profile
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Payment Settings" key="payment">
            <Form
              form={paymentForm}
              layout="vertical"
              initialValues={{
                bankName: 'National Bank',
                accountNumber: '****1234',
                routingNumber: '****5678',
                accountType: 'checking',
                accountHolder: 'My Business LLC',
                currency: 'USD',
                autoSettlement: true,
              }}
              onFinish={onPaymentFinish}
            >
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Avatar
                  size={64}
                  icon={<BankOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
              </div>

              <Form.Item
                label="Bank Name"
                name="bankName"
                rules={[{ required: true, message: 'Please input bank name!' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Account Number"
                name="accountNumber"
                rules={[
                  { required: true, message: 'Please input account number!' },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Routing Number"
                name="routingNumber"
                rules={[
                  { required: true, message: 'Please input routing number!' },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item label="Account Type" name="accountType">
                <Select>
                  <Option value="checking">Checking</Option>
                  <Option value="savings">Savings</Option>
                  <Option value="business">Business</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="Account Holder Name"
                name="accountHolder"
                rules={[
                  {
                    required: true,
                    message: 'Please input account holder name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item label="Currency" name="currency">
                <Select>
                  <Option value="USD">USD</Option>
                  <Option value="EUR">EUR</Option>
                  <Option value="GBP">GBP</Option>
                  <Option value="JPY">JPY</Option>
                  <Option value="CNY">CNY</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="Auto Settlement"
                name="autoSettlement"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Save Payment Settings
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Security" key="security">
            <Form
              form={securityForm}
              layout="vertical"
              initialValues={{
                twoFactorAuth: true,
                emailAlerts: true,
                ipRestriction: false,
              }}
              onFinish={onSecurityFinish}
            >
              <Form.Item
                label="Current Password"
                name="currentPassword"
                rules={[
                  {
                    required: true,
                    message: 'Please input your current password!',
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="New Password"
                name="newPassword"
                rules={[
                  {
                    required: true,
                    message: 'Please input your new password!',
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="Confirm New Password"
                name="confirmPassword"
                dependencies={['newPassword']}
                rules={[
                  {
                    required: true,
                    message: 'Please confirm your new password!',
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('The two passwords do not match!'),
                      );
                    },
                  }),
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="Two-Factor Authentication"
                name="twoFactorAuth"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Email Alerts for Login Attempts"
                name="emailAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="IP Address Restriction"
                name="ipRestriction"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Update Security Settings
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Notifications" key="notifications">
            <Form
              form={notificationForm}
              layout="vertical"
              initialValues={{
                emailNotifications: true,
                smsNotifications: false,
                transactionAlerts: true,
                settlementAlerts: true,
                securityAlerts: true,
              }}
              onFinish={onNotificationFinish}
            >
              <Form.Item
                label="Email Notifications"
                name="emailNotifications"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="SMS Notifications"
                name="smsNotifications"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Transaction Alerts"
                name="transactionAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Settlement Alerts"
                name="settlementAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Security Alerts"
                name="securityAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Save Notification Settings
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default MerchantSettings;
