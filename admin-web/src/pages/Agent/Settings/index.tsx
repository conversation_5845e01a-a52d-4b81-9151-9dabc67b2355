import { SaveOutlined, UploadOutlined, UserOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Avatar,
  Button,
  Card,
  Form,
  Input,
  Switch,
  Tabs,
  Upload,
  message,
} from 'antd';
import React from 'react';

const { TabPane } = Tabs;

const AgentSettings: React.FC = () => {
  const [profileForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  const onProfileFinish = (values: any) => {
    //console.log('Profile settings:', values);
    message.success('Profile settings saved successfully');
  };

  const onSecurityFinish = (values: any) => {
    //console.log('Security settings:', values);
    message.success('Security settings saved successfully');
  };

  const onNotificationFinish = (values: any) => {
    //console.log('Notification settings:', values);
    message.success('Notification settings saved successfully');
  };

  return (
    <PageContainer title="Agent Settings">
      <Card>
        <Tabs defaultActiveKey="profile">
          <TabPane tab="Profile" key="profile">
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                name: '<PERSON>',
                email: '<EMAIL>',
                phone: '+****************',
                company: 'Smith Financial Services',
                address: '123 Main St, Anytown, USA',
                bio: 'Financial services professional with 10+ years of experience.',
              }}
              onFinish={onProfileFinish}
            >
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Avatar size={100} icon={<UserOutlined />} />
                <div style={{ marginTop: 16 }}>
                  <Upload>
                    <Button icon={<UploadOutlined />}>Change Avatar</Button>
                  </Upload>
                </div>
              </div>

              <Form.Item
                label="Full Name"
                name="name"
                rules={[{ required: true, message: 'Please input your name!' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Email"
                name="email"
                rules={[
                  { required: true, message: 'Please input your email!' },
                  { type: 'email', message: 'Please enter a valid email!' },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item label="Phone Number" name="phone">
                <Input />
              </Form.Item>

              <Form.Item label="Company" name="company">
                <Input />
              </Form.Item>

              <Form.Item label="Address" name="address">
                <Input.TextArea rows={2} />
              </Form.Item>

              <Form.Item label="Bio" name="bio">
                <Input.TextArea rows={4} />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Save Profile
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Security" key="security">
            <Form
              form={securityForm}
              layout="vertical"
              initialValues={{
                twoFactorAuth: true,
                emailAlerts: true,
                ipRestriction: false,
              }}
              onFinish={onSecurityFinish}
            >
              <Form.Item
                label="Current Password"
                name="currentPassword"
                rules={[
                  {
                    required: true,
                    message: 'Please input your current password!',
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="New Password"
                name="newPassword"
                rules={[
                  {
                    required: true,
                    message: 'Please input your new password!',
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="Confirm New Password"
                name="confirmPassword"
                dependencies={['newPassword']}
                rules={[
                  {
                    required: true,
                    message: 'Please confirm your new password!',
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('The two passwords do not match!'),
                      );
                    },
                  }),
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label="Two-Factor Authentication"
                name="twoFactorAuth"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Email Alerts for Login Attempts"
                name="emailAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="IP Address Restriction"
                name="ipRestriction"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Update Security Settings
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="Notifications" key="notifications">
            <Form
              form={notificationForm}
              layout="vertical"
              initialValues={{
                emailNotifications: true,
                smsNotifications: false,
                newMerchantAlerts: true,
                transactionAlerts: true,
                settlementAlerts: true,
                securityAlerts: true,
              }}
              onFinish={onNotificationFinish}
            >
              <Form.Item
                label="Email Notifications"
                name="emailNotifications"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="SMS Notifications"
                name="smsNotifications"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="New Merchant Alerts"
                name="newMerchantAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Transaction Alerts"
                name="transactionAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Settlement Alerts"
                name="settlementAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="Security Alerts"
                name="securityAlerts"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                >
                  Save Notification Settings
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default AgentSettings;
