import { message } from 'antd';
import { useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getMerchantWallet } from '@/api/interfaces/merchant-wallet/merchant-wallet';
import type {
  MerchantAssetsOverview,
  MerchantWalletsQueryParams,
  AdjustBalanceRequest,
  AdjustBalanceResponse,
  AssetManagementRouteParams,
} from './types';

// 获取API实例
const merchantWalletApi = getMerchantWallet();

/**
 * 商户资产管理页面的自定义 Hook
 */
export const useMerchantAssetManagement = () => {
  const params = useParams<AssetManagementRouteParams>();
  const [loading, setLoading] = useState(false);
  const [assetsOverview, setAssetsOverview] = useState<MerchantAssetsOverview | null>(null);

  // 获取商户资产概览
  const fetchMerchantAssets = useCallback(async (merchantId: number) => {
    setLoading(true);
    try {
      const response = await merchantWalletApi.getApiSystemMerchantsMerchantIdAssets(merchantId);
      // response 已经是解包后的 data 对象
      if (response) {
        const data: MerchantAssetsOverview = {
          merchantId: response.merchantId || merchantId,
          merchantName: response.merchantName || '',
          assets: response.assets || [],
          totalAssets: response.totalAssets || 0,
        };
        setAssetsOverview(data);
        return { success: true, data };
      } else {
        message.error('获取商户资产失败');
        return { success: false, data: null };
      }
    } catch (error: any) {
      console.error('获取商户资产失败:', error);
      const errorMsg = error?.response?.data?.message || error.message || '获取商户资产失败';
      message.error(errorMsg);
      return { success: false, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  // 调整商户余额
  const adjustMerchantBalance = useCallback(async (merchantId: number, request: AdjustBalanceRequest) => {
    setLoading(true);
    try {
      const response = await merchantWalletApi.postApiSystemMerchantsMerchantIdWalletsAdjust(merchantId, {
        symbol: request.symbol,
        type: request.type,
        walletType: request.walletType,
        amount: request.amount as any, // API 需要 decimal 类型，但我们传入字符串
        reason: request.reason,
        reference: request.reference,
      });
      
      // response 已经是解包后的 data 对象
      if (response) {
        const result: AdjustBalanceResponse = {
          success: response.success || true,
          transactionId: response.transactionId || 0,
          balanceBefore: String(response.balanceBefore || ''),
          balanceAfter: String(response.balanceAfter || ''),
          formattedBalanceBefore: response.formattedBalanceBefore || '',
          formattedBalanceAfter: response.formattedBalanceAfter || '',
        };
        message.success('余额调整成功');
        return { success: true, data: result };
      } else {
        message.error('调整余额失败');
        return { success: false, data: null };
      }
    } catch (error: any) {
      console.error('调整余额失败:', error);
      const errorMsg = error?.response?.data?.message || error.message || '调整余额失败';
      message.error(errorMsg);
      return { success: false, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取路由中的商户ID
  const getCurrentMerchantId = useCallback(() => {
    // 首先从URL参数获取
    if (params.merchantId) {
      return parseInt(params.merchantId, 10);
    }
    // 如果URL参数中没有，从查询参数获取
    const searchParams = new URLSearchParams(window.location.search);
    const merchantIdStr = searchParams.get('merchantId');
    return merchantIdStr ? parseInt(merchantIdStr, 10) : null;
  }, [params.merchantId]);

  return {
    loading,
    assetsOverview,
    fetchMerchantAssets,
    adjustMerchantBalance,
    getCurrentMerchantId,
  };
};

/**
 * 商户钱包列表管理的自定义 Hook
 */
export const useMerchantWalletsList = () => {
  const [loading, setLoading] = useState(false);

  // 获取商户钱包列表
  const fetchMerchantWallets = useCallback(
    async (params: MerchantWalletsQueryParams) => {
      setLoading(true);
      //console.log('获取商户钱包列表参数:', params);
      
      try {
        // 构建API参数
        const apiParams = {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          merchantId: params.merchantId,
          symbol: params.symbol,
          hasBalance: params.hasBalance,
          dateRange: params.dateRange ? params.dateRange.join(',') : undefined,
          export: params.export,
        };
        
        const response = await merchantWalletApi.getApiSystemMerchantWallets(apiParams);
        
        // response 已经是解包后的 data 对象
        if (response) {
          return {
            data: response.data || [],
            success: true,
            total: response.page?.totalSize || 0,
          };
        } else {
          message.error('获取商户钱包列表失败');
          return { data: [], success: false, total: 0 };
        }
      } catch (error: any) {
        console.error('获取商户钱包列表错误:', error);
        const errorMsg = error?.response?.data?.message || error.message || '获取商户钱包列表失败';
        message.error(errorMsg);
        return { data: [], success: false, total: 0 };
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  return {
    loading,
    fetchMerchantWallets,
  };
};

/**
 * 余额调整表单的自定义 Hook
 */
export const useAdjustBalanceForm = () => {
  const [adjusting, setAdjusting] = useState(false);

  const submitAdjustment = useCallback(async (merchantId: number, request: AdjustBalanceRequest) => {
    setAdjusting(true);
    try {
      const response = await merchantWalletApi.postApiSystemMerchantsMerchantIdWalletsAdjust(merchantId, {
        symbol: request.symbol,
        type: request.type,
        walletType: request.walletType,
        amount: request.amount as any, // API 需要 decimal 类型，但我们传入字符串
        reason: request.reason,
        reference: request.reference,
      });
      
      // response 已经是解包后的 data 对象
      if (response) {
        const result: AdjustBalanceResponse = {
          success: response.success || true,
          transactionId: response.transactionId || 0,
          balanceBefore: String(response.balanceBefore || ''),
          balanceAfter: String(response.balanceAfter || ''),
          formattedBalanceBefore: response.formattedBalanceBefore || '',
          formattedBalanceAfter: response.formattedBalanceAfter || '',
        };
        message.success(
          `余额调整成功！交易ID: ${result.transactionId}，调整后余额: ${result.formattedBalanceAfter}`
        );
        return { success: true, data: result };
      } else {
        message.error('余额调整失败');
        return { success: false, data: null };
      }
    } catch (error: any) {
      console.error('余额调整失败:', error);
      const errorMsg = error?.response?.data?.message || error.message || '余额调整失败';
      message.error(errorMsg);
      return { success: false, data: null };
    } finally {
      setAdjusting(false);
    }
  }, []);

  return {
    adjusting,
    submitAdjustment,
  };
};