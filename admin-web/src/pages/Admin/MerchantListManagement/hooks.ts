import { message } from 'antd';
import { useCallback, useState } from 'react';
import { getSystemMerchant } from '../../../api/interfaces/system-merchant/system-merchant';
import type {
  AdminApiApiSystemV1AddMerchantReq, // Added for Create API Key
  AdminApiApiSystemV1GenerateMerchantApiKeyRes,
  AdminApiApiSystemV1MerchantInfoType,
  GetApiSystemMerchantsParams, // Added for reset password

  // DeleteApiSystemMerchantsParams, // We'll construct this inline
  PostApiSystemMerchantsMerchantIdApikeysBody,
  PutApiSystemMerchantsMerchantIdBody, // <-- Import this type
  PutApiSystemMerchantsMerchantIdGoogle2faBody, // Added for reset 2FA
  PutApiSystemMerchantsMerchantIdStatusBody, // <-- Import this type
} from '../../../api/model';
import { MerchantListParams } from './types'; // MerchantStatus is used for param conversion

/**
 * 商户列表管理页面的自定义 Hook
 */
export const useMerchantListManagement = () => {
  const [loading, setLoading] = useState(false);

  // 获取商户列表数据
  const fetchMerchants = useCallback(
    async (
      params: MerchantListParams & { sorter?: any; filter?: any },
    ): Promise<{
      data: AdminApiApiSystemV1MerchantInfoType[];
      success: boolean;
      total: number;
    }> => {
      setLoading(true);
      //console.log('Fetching merchants with ProTable params:', params);

      const apiParams: GetApiSystemMerchantsParams = {
        page: params.current || 1,
        pageSize: params.pageSize || 10,
        // Map search fields from ProTable to API parameters
        merchantName: params.merchantName || params.name, // Support both merchantName and name
        businessName: params.businessName,
        email: params.email,
        phone: params.phone,
        telegramUsername: params.telegramUsername, // Add telegram username search
        telegramId: params.telegramId, // Add telegram ID search
        dateRange: params.dateRange, // Pass dateRange directly
        export: params.export,
      };

      // Remove undefined values to avoid sending empty parameters
      Object.keys(apiParams).forEach((key) => {
        if (
          apiParams[key as keyof GetApiSystemMerchantsParams] === undefined ||
          apiParams[key as keyof GetApiSystemMerchantsParams] === null ||
          apiParams[key as keyof GetApiSystemMerchantsParams] === ''
        ) {
          delete apiParams[key as keyof GetApiSystemMerchantsParams];
        }
      });

      // Status conversion: ProTable might send string "1", "0", "-1" or number
      // API expects number for status (-1:待审核, 0:禁用, 1:启用)
      // Status values: Active = 1, Inactive = 0, Pending = -1
      if (
        params.status !== undefined &&
        params.status !== null &&
        params.status !== ''
      ) {
        apiParams.status =
          typeof params.status === 'string'
            ? parseInt(params.status, 10)
            : params.status;
      }

      // TODO: Handle sorter if API supports it.
      // Example: if (params.sorter) { apiParams.orderBy = Object.keys(params.sorter)[0]; apiParams.orderDirection = params.sorter[Object.keys(params.sorter)[0]] === 'ascend' ? 'asc' : 'desc'; }

      //console.log('Adapted API params:', apiParams);

      try {
        const response = await getSystemMerchant().getApiSystemMerchants(
          apiParams,
        );
        setLoading(false);
        if (response) {
          // Assuming response is directly AdminApiApiSystemV1GetMerchantListRes
          return {
            data: response.data || [],
            success: true,
            total: response.page?.totalSize || 0,
          };
        } else {
          // This case might not be reachable if an error always throws or response is always an object
          message.error('获取商户列表失败: 响应为空');
          return { data: [], success: false, total: 0 };
        }
      } catch (error: any) {
        setLoading(false);
        console.error('获取商户列表错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '获取商户列表失败，请重试';
        message.error(errorMsg);
        return { data: [], success: false, total: 0 };
      }
    },
    [],
  );

  // 新增商户
  const addMerchant = useCallback(
    async (merchantData: AdminApiApiSystemV1AddMerchantReq & { user_id?: number }) => {
      setLoading(true);
      try {
        await getSystemMerchant().postApiSystemMerchants(merchantData as any);
        setLoading(false);
        message.success('新增商户成功');
        return { success: true };
      } catch (error: any) {
        setLoading(false);
        console.error('新增商户错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '新增商户失败，请重试';
        message.error(errorMsg);
        return { success: false };
      }
    },
    [],
  );

  // 更新商户
  const updateMerchant = useCallback(
    async (
      merchantId: number,
      dataToUpdate: PutApiSystemMerchantsMerchantIdBody & { user_id?: number },
    ) => {
      setLoading(true);
      try {
        await getSystemMerchant().putApiSystemMerchantsMerchantId(
          merchantId,
          dataToUpdate as any,
        );
        setLoading(false);
        message.success('更新商户成功');
        return { success: true };
      } catch (error: any) {
        setLoading(false);
        console.error('更新商户错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '更新商户失败，请重试';
        message.error(errorMsg);
        return { success: false };
      }
    },
    [],
  );

  // 更新商户状态 (用于审核)
  const updateMerchantStatus = useCallback(
    async (
      merchantId: number,
      statusData: PutApiSystemMerchantsMerchantIdStatusBody,
    ) => {
      setLoading(true);
      try {
        await getSystemMerchant().putApiSystemMerchantsMerchantIdStatus(
          merchantId,
          statusData,
        );
        setLoading(false);
        message.success('商户状态更新成功');
        return { success: true };
      } catch (error: any) {
        setLoading(false);
        console.error('更新商户状态错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '更新商户状态失败，请重试';
        message.error(errorMsg);
        return { success: false };
      }
    },
    [],
  );

  // 删除商户
  const deleteMerchant = useCallback(async (merchantId: number) => {
    setLoading(true);
    try {
      // API expects { merchantIds: number[] }
      await getSystemMerchant().deleteApiSystemMerchants({
        merchantIds: [merchantId],
      });
      setLoading(false);
      message.success(`删除商户 ID: ${merchantId} 成功`);
      return { success: true };
    } catch (error: any) {
      setLoading(false);
      console.error('删除商户错误:', error);
      const errorMsg =
        error?.response?.data?.message ||
        error.message ||
        '删除商户失败，请重试';
      message.error(errorMsg);
      return { success: false };
    }
  }, []);

  // 查看商户详情
  const viewMerchantDetail = useCallback(async (merchantId: number) => {
    setLoading(true);
    try {
      const response =
        await getSystemMerchant().getApiSystemMerchantsMerchantId(merchantId);
      setLoading(false);
      if (response && response.data) {
        // //console.log('查看商户详情:', response.data);
        // message.info(`查看商户 ${response.data.merchantName} 的详情`); // Message can be removed if a modal shows up
        return { success: true, data: response.data };
      } else {
        message.error('获取商户详情失败: 响应数据不正确');
        return { success: false, data: null };
      }
    } catch (error: any) {
      setLoading(false);
      console.error('查看商户详情错误:', error);
      const errorMsg =
        error?.response?.data?.message ||
        error.message ||
        '获取商户详情失败，请重试';
      message.error(errorMsg);
      return { success: false, data: null };
    }
  }, []);

  // 重置商户2FA
  const resetMerchant2FA = useCallback(async (merchantId: number) => {
    setLoading(true);
    try {
      // API期望一个body参数，即使它可能是空的或未被严格使用。
      // 根据 PutApiSystemMerchantsMerchantIdGoogle2faBody 定义 ({ [key: string]: unknown })，传递空对象。
      await getSystemMerchant().putApiSystemMerchantsMerchantIdGoogle2fa(
        merchantId,
        {} as PutApiSystemMerchantsMerchantIdGoogle2faBody,
      );
      setLoading(false);
      message.success('重置2FA成功');
      return { success: true };
    } catch (error: any) {
      setLoading(false);
      console.error('重置2FA错误:', error);
      const errorMsg =
        error?.response?.data?.message ||
        error.message ||
        '重置2FA失败，请重试';
      message.error(errorMsg);
      return { success: false };
    }
  }, []);

  // 重置商户密码
  const resetMerchantPassword = useCallback(
    async (
      merchantId: number,
      newPasswordDetails: { password_New: string },
    ) => {
      setLoading(true);
      try {
        await getSystemMerchant().putApiSystemMerchantsMerchantIdPassword(
          merchantId,
          {
            newPassword: newPasswordDetails.password_New,
          },
        );
        setLoading(false);
        message.success('重置密码成功');
        return { success: true };
      } catch (error: any) {
        setLoading(false);
        console.error('重置密码错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '重置密码失败，请重试';
        message.error(errorMsg);
        return { success: false };
      }
    },
    [],
  );

  // 创建商户API Key
  const createMerchantApiKey = useCallback(
    async (
      merchantId: number,
      apiKeyData: PostApiSystemMerchantsMerchantIdApikeysBody,
    ): Promise<AdminApiApiSystemV1GenerateMerchantApiKeyRes | null> => {
      setLoading(true);
      try {
        const response =
          await getSystemMerchant().postApiSystemMerchantsMerchantIdApikeys(
            merchantId,
            apiKeyData,
          );
        setLoading(false);
        message.success('API Key 创建成功');
        return response; // Return the full response object which includes apiKey, secretKey, apiKeyId
      } catch (error: any) {
        setLoading(false);
        console.error('创建API Key错误:', error);
        const errorMsg =
          error?.response?.data?.message ||
          error.message ||
          '创建API Key失败，请重试';
        message.error(errorMsg);
        // throw error; // Re-throw to be caught by the modal's try-catch
        return null; // Or return null to indicate failure to the modal
      }
    },
    [],
  );

  return {
    loading,
    fetchMerchants, // Renamed
    addMerchant,
    updateMerchant,
    updateMerchantStatus, // <-- Add this to returned object
    deleteMerchant,
    viewMerchantDetail,
    resetMerchant2FA, // <-- Add this to returned object
    resetMerchantPassword, // <-- Add this to returned object
    createMerchantApiKey, // <-- Add this to returned object
  };
};
