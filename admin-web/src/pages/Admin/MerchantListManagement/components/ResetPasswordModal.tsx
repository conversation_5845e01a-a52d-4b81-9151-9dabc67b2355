import { ProForm, ProFormText } from '@ant-design/pro-components';
import { Form, Modal } from 'antd'; // Removed Input as ProFormText handles it
import React from 'react';

interface ResetPasswordModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: { password_New: string }) => Promise<void>;
  loading: boolean;
  merchantName?: string;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  visible,
  onCancel,
  onSubmit,
  loading,
  merchantName,
}) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit({ password_New: values.newPassword });
      // Do not reset fields here, Modal's destroyOnClose will handle it or parent can reset if needed after successful submit
      // form.resetFields();
    } catch (errorInfo) {
      // Antd form validation errors are usually handled by the form itself by displaying messages.
      // //console.log('校验失败:', errorInfo);
      // message.error('请检查表单输入。'); // Generic message if needed
    }
  };

  return (
    <Modal
      title={`为 ${merchantName || '商户'} 重置密码`}
      open={visible}
      onCancel={() => {
        form.resetFields(); // Reset fields on cancel
        onCancel();
      }}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnClose // Ensures form state is reset when modal is closed and reopened
    >
      <ProForm
        form={form}
        layout="vertical"
        // ProForm's onFinish is an alternative to Modal's onOk, choose one for clarity.
        // Here, Modal's onOk is used, so ProForm's onFinish can be removed or not used for submission.
        onFinish={async (values) => {
          // This onFinish is for ProForm's internal submit button, which we are hiding.
          // If Modal's onOk is the primary submit trigger, this might not be strictly necessary
          // unless ProForm's submitter is used.
          //console.log('ProForm onFinish triggered with: ', values);
        }}
        submitter={{
          // Hide ProForm's default submit/reset buttons, using Modal's buttons instead.
          render: () => [],
        }}
        // It's good practice to reset fields when the modal is closed,
        // especially if not using destroyOnClose or if specific reset logic is needed.
        // However, destroyOnClose is generally simpler.
      >
        <ProFormText.Password
          name="newPassword"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码至少6位' },
            { max: 30, message: '密码最多30位' },
          ]}
        />
        <ProFormText.Password
          name="confirmPassword"
          label="确认密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        />
      </ProForm>
    </Modal>
  );
};

export default ResetPasswordModal;
