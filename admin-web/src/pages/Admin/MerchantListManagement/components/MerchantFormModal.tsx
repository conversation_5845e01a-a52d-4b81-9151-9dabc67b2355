import type {
  AdminApiApiSystemV1AddMerchantReq,
  PutApiSystemMerchantsMerchantIdBody,
  AdminApiApiSystemV1UserInfoType,
} from '@/api/model'; // Combined imports
import { getSystemUser } from '@/api/interfaces/system-user/system-user';
import { Button, Form, Input, Modal, Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import React, { useCallback, useEffect, useState } from 'react';

interface MerchantFormModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (
    values:
      | AdminApiApiSystemV1AddMerchantReq
      | PutApiSystemMerchantsMerchantIdBody,
  ) => Promise<void>; // Updated type
  loading?: boolean;
  initialValues?: Partial<
    AdminApiApiSystemV1AddMerchantReq | PutApiSystemMerchantsMerchantIdBody
  >; // Updated type
  isEditMode?: boolean; // Added prop
}

const MerchantFormModal: React.FC<MerchantFormModalProps> = ({
  visible,
  onCancel,
  onSubmit,
  loading,
  initialValues,
  isEditMode, // Destructure new prop
}) => {
  // Adjust form typings if necessary, though antd Form can be flexible.
  // For stricter typing, you might use a generic or conditional type based on isEditMode.
  const [form] = Form.useForm<
    AdminApiApiSystemV1AddMerchantReq | PutApiSystemMerchantsMerchantIdBody
  >();

  // State for user search dropdown
  const [userOptions, setUserOptions] = useState<
    { label: string; value: number; data: AdminApiApiSystemV1UserInfoType }[]
  >([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);

  // User search function
  const searchUsers = async (searchValue: string) => {
    if (!searchValue || searchValue.length < 2) {
      setUserOptions([]);
      return;
    }

    setUserSearchLoading(true);
    try {
      const response = await getSystemUser().getApiSystemUsers({
        page: 1,
        pageSize: 20,
        // account: searchValue,
        telegramId: searchValue,
        // telegramUsername: searchValue,
      });

      if (response?.data) {
        const options = response.data.map((user: AdminApiApiSystemV1UserInfoType) => ({
          label: `${user.account || ''} - ${user.telegramUsername || user.telegramId || 'N/A'}`,
          value: user.id!,
          data: user,
        }));
        setUserOptions(options);
      }
    } catch (error) {
      console.error('Failed to search users:', error);
      setUserOptions([]);
    } finally {
      setUserSearchLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearchUsers = useCallback(
    debounce((value: string) => {
      searchUsers(value);
    }, 300),
    [],
  );

  useEffect(() => {
    if (visible) {
      form.resetFields();
      setUserOptions([]);
      if (initialValues) {
        if (isEditMode) {
          // For edit mode, set fields directly
          form.setFieldsValue(initialValues);
        } else {
          // For add mode, set all initialValues (which might include userPassword if provided)
          form.setFieldsValue(initialValues);
        }
      }
    }
  }, [visible, initialValues, form, isEditMode]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearchUsers.cancel();
    };
  }, [debouncedSearchUsers]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit(values);
    } catch (errorInfo) {
      //console.log('Validation Failed:', errorInfo);
    }
  };

  const handleReset = () => {
    form.resetFields();
  };

  return (
    <Modal
      title={isEditMode ? '编辑商户' : '新建商户'} // Dynamic title
      open={visible}
      onCancel={onCancel}
      confirmLoading={loading}
      footer={[
        <Button key="reset" onClick={handleReset}>
          重置
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleOk}
        >
          提交
        </Button>,
      ]}
      maskClosable={false}
    >
      <Form form={form} layout="vertical" name="merchant_form">
        <Form.Item
          name="email"
          label="商户邮箱"
          rules={[
            { required: true, message: '请输入商户邮箱!' },
            { type: 'email', message: '请输入有效的邮箱地址!' },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="merchantName"
          label="商户名称"
          rules={[
            { required: true, message: '请输入商户名称!' },
            { min: 2, message: '商户名称至少2个字符!' },
            { max: 150, message: '商户名称最多150个字符!' },
            {
              pattern: /^[a-zA-Z0-9]([a-zA-Z0-9_-]*[a-zA-Z0-9])?$/,
              message: '商户名称只能包含字母、数字、下划线或连字符，且不能以下划线或连字符开头或结尾!',
            },
            {
              pattern: /^(?!.*[-_]{2}).*$/,
              message: '商户名称不能包含连续的下划线或连字符!',
            },
          ]}
          extra="只能包含字母、数字、下划线或连字符，且不能以下划线或连字符开头或结尾"
        >
          <Input placeholder="请输入商户名称" disabled={isEditMode} />
        </Form.Item>
        <Form.Item
          name="businessName"
          label="业务名称"
          rules={[{ max: 255, message: '业务名称最多255个字符!' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="phone"
          label="联系电话"
          rules={[{ max: 50, message: '电话最多50个字符!' }]}
        >
          <Input />
        </Form.Item>


        <Form.Item
          name="websiteUrl"
          label="商户网站URL"
          rules={[
            { type: 'url', message: '请输入有效的URL地址!' },
            { max: 255, message: '商户网站URL最多255个字符!' },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="contactEmail"
          label="备用联系邮箱"
          rules={[{ type: 'email', message: '请输入有效的邮箱地址!' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="associatedUserId"
          label="关联用户"
          rules={[{ required: false, message: '请选择关联用户!' }]}
          extra="搜索并选择要与此商户关联的用户（可选）"
        >
          <Select
            showSearch
            placeholder="输入用户账号、Telegram ID或Telegram用户名进行搜索"
            notFoundContent={userSearchLoading ? <Spin size="small" /> : '暂无数据'}
            filterOption={false}
            onSearch={debouncedSearchUsers}
            options={userOptions}
            allowClear
            style={{ width: '100%' }}
          />
        </Form.Item>
        {isEditMode && (
          <Form.Item
            name="callbackUrl"
            label="回调URL"
            rules={[
              { type: 'url', message: '请输入有效的URL地址!' },
              { max: 512, message: '回调URL最多512个字符!' },
            ]}
          >
            <Input />
          </Form.Item>
        )}
        <Form.Item
          name="notes"
          label="备注"
          rules={[{ max: 200, message: '备注最多200个字符!' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        {/* 密码字段 - 只在创建模式下显示 */}
        {!isEditMode && (
          <>
            <Form.Item
              name="password"
              label="登录密码"
              rules={[
                { required: true, message: '请输入登录密码!' },
                { min: 6, message: '密码至少6个字符!' },
                { max: 50, message: '密码最多50个字符!' },
              ]}
            >
              <Input.Password placeholder="请输入登录密码" />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致!'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder="请再次输入密码" />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  );
};

export default MerchantFormModal;
