import type { AdminApiApiSystemV1MerchantInfoType } from '@/api/model';
import { Button, Descriptions, Form, Input, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

const { Option } = Select;

interface ReviewFormData {
  reviewStatus: boolean;
  reviewNotes?: string;
}

interface ReviewMerchantModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: { status: boolean; notes?: string }) => Promise<void>;
  merchantInfo: AdminApiApiSystemV1MerchantInfoType | null;
  loading?: boolean;
}

const ReviewMerchantModal: React.FC<ReviewMerchantModalProps> = ({
  visible,
  onCancel,
  onSubmit,
  merchantInfo,
  loading,
}) => {
  const [form] = Form.useForm<ReviewFormData>();

  useEffect(() => {
    if (visible) {
      form.resetFields();
      // Optionally set initial values, e.g., default status or clear notes
      // form.setFieldsValue({ reviewStatus: undefined, reviewNotes: '' });
    }
  }, [visible, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit({
        status: values.reviewStatus,
        notes: values.reviewNotes,
      });
    } catch (errorInfo) {
      //console.log('Validation Failed:', errorInfo);
    }
  };

  return (
    <Modal
      title="商户审核"
      open={visible}
      onCancel={onCancel}
      confirmLoading={loading}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleOk}
        >
          提交审核
        </Button>,
      ]}
      destroyOnClose
      maskClosable={false}
      width={600}
    >
      {merchantInfo && (
        <Descriptions bordered column={1} style={{ marginBottom: 24 }}>
          <Descriptions.Item label="商户ID">
            {merchantInfo.merchantId}
          </Descriptions.Item>
          <Descriptions.Item label="商户名称">
            {merchantInfo.merchantName}
          </Descriptions.Item>
          <Descriptions.Item label="当前状态">待审核</Descriptions.Item>
          {/* Assuming this modal is only for 'Pending' status */}
        </Descriptions>
      )}
      <Form form={form} layout="vertical" name="review_merchant_form">
        <Form.Item
          name="reviewStatus"
          label="审核结果"
          rules={[{ required: true, message: '请选择审核结果!' }]}
        >
          <Select placeholder="请选择审核结果">
            <Option value={true}>通过</Option>
            <Option value={false}>拒绝</Option>
          </Select>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.reviewStatus !== currentValues.reviewStatus
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('reviewStatus') === false ? (
              <Form.Item
                name="reviewNotes"
                label="审核备注 (拒绝时必填)"
                rules={[{ required: true, message: '拒绝时请输入审核备注!' }]}
              >
                <Input.TextArea rows={4} placeholder="请输入审核备注" />
              </Form.Item>
            ) : (
              <Form.Item name="reviewNotes" label="审核备注 (可选)">
                <Input.TextArea rows={4} placeholder="请输入审核备注" />
              </Form.Item>
            )
          }
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReviewMerchantModal;
