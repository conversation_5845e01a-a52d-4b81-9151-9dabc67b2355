// src/pages/Admin/MerchantListManagement/index.tsx
import CommonProTable from '@/components/CommonProTable';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
} from '@ant-design/pro-components';
import { Button, Popconfirm, Space, message } from 'antd';
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { merchantListColumns } from './constants'; // Will need updates for dataIndex
import { useMerchantListManagement } from './hooks';
// import { Merchant } from './types'; // Replaced by AdminApiApiSystemV1MerchantInfoType
import type {
  AdminApiApiSystemV1AddMerchantReq, // Added for Create API Key
  AdminApiApiSystemV1GenerateMerchantApiKeyRes,
  AdminApiApiSystemV1MerchantInfoType, // For edit
  PostApiSystemMerchantsMerchantIdApikeysBody,
  PutApiSystemMerchantsMerchantIdBody, // For edit
} from '@/api/model';
import CreateApiKeyModal from './components/CreateApiKeyModal'; // Added for Create API Key
import MerchantFormModal from './components/MerchantFormModal';
import ResetPasswordModal from './components/ResetPasswordModal'; // Added for reset password
import ReviewMerchantModal from './components/ReviewMerchantModal'; // Will be added later
import { MerchantStatus } from './types'; // For checking status

const MerchantListManagement: React.FC = () => {
  const navigate = useNavigate();
  const actionRef = useRef<ActionType>();

  // Modal visibility states
  const [isAddModalVisible, setIsAddModalVisible] = useState(false); // For new merchant
  const [isEditModalVisible, setIsEditModalVisible] = useState(false); // For editing merchant
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false); // For reviewing merchant
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] =
    useState(false); // For reset password
  const [isCreateApiKeyModalVisible, setIsCreateApiKeyModalVisible] =
    useState(false); // For Create API Key

  // Data states for modals
  const [editingMerchant, setEditingMerchant] =
    useState<AdminApiApiSystemV1MerchantInfoType | null>(null);
  const [reviewingMerchant, setReviewingMerchant] =
    useState<AdminApiApiSystemV1MerchantInfoType | null>(null);
  const [resettingPasswordMerchant, setResettingPasswordMerchant] =
    useState<AdminApiApiSystemV1MerchantInfoType | null>(null); // For reset password
  const [selectedMerchantForApiKey, setSelectedMerchantForApiKey] =
    useState<AdminApiApiSystemV1MerchantInfoType | null>(null); // For Create API Key

  const {
    loading,
    fetchMerchants,
    addMerchant,
    updateMerchant, // Added from hook
    updateMerchantStatus, // Added from hook
    deleteMerchant,
    resetMerchant2FA, // Added for reset 2FA
    resetMerchantPassword, // Added for reset password
    createMerchantApiKey, // Added for Create API Key
    // viewMerchantDetail, // Removed as per requirement
  } = useMerchantListManagement();

  // 处理删除操作
  const handleDelete = async (merchantId?: number) => {
    if (merchantId === undefined) {
      message.error('无法删除商户：ID 未定义');
      return;
    }
    const result = await deleteMerchant(merchantId);
    if (result.success) {
      actionRef.current?.reload(); // 刷新表格
    }
  };

  // 处理编辑操作
  const handleEdit = (record: AdminApiApiSystemV1MerchantInfoType) => {
    setEditingMerchant(record);
    setIsEditModalVisible(true);
  };

  // 处理审核操作
  const handleReview = (record: AdminApiApiSystemV1MerchantInfoType) => {
    setReviewingMerchant(record);
    setIsReviewModalVisible(true);
  };

  // 处理新增操作
  const handleAdd = () => {
    setEditingMerchant(null); // Ensure editingMerchant is null for add mode
    setIsAddModalVisible(true);
  };

  // 处理资产管理操作
  const handleAssetManagement = (record: AdminApiApiSystemV1MerchantInfoType) => {
    navigate(`/admin/merchant/assets?merchantId=${record.merchantId}`);
  };

  // Add/Edit Modal Submit Logic
  const handleFormSubmit = async (
    values:
      | (AdminApiApiSystemV1AddMerchantReq & { associatedUserId?: number })
      | (PutApiSystemMerchantsMerchantIdBody & { associatedUserId?: number }),
  ) => {
    // Extract associatedUserId from values
    const { associatedUserId, ...apiValues } = values;
    
    let result;
    if (editingMerchant && editingMerchant.merchantId !== undefined) {
      // For edit mode, include user_id in the request
      const updateData = associatedUserId 
        ? { ...apiValues, user_id: associatedUserId }
        : apiValues;
      result = await updateMerchant(
        editingMerchant.merchantId,
        updateData as PutApiSystemMerchantsMerchantIdBody,
      );
    } else {
      // For create mode, include user_id in the request
      const createData = associatedUserId 
        ? { ...apiValues, user_id: associatedUserId }
        : apiValues;
      result = await addMerchant(createData as AdminApiApiSystemV1AddMerchantReq);
    }

    if (result.success) {
      setIsAddModalVisible(false);
      setIsEditModalVisible(false);
      setEditingMerchant(null); // Reset editing merchant
      actionRef.current?.reload();
    }
  };

  // Review Modal Submit Logic
  const handleReviewSubmit = async (reviewData: {
    status: boolean;
    notes?: string;
  }) => {
    if (reviewingMerchant && reviewingMerchant.merchantId !== undefined) {
      const result = await updateMerchantStatus(
        reviewingMerchant.merchantId,
        {
          status: reviewData.status ? 1 : 0, // Convert boolean to number (1 for approved, 0 for rejected)
          notes: reviewData.notes,
        },
      );
      if (result.success) {
        setIsReviewModalVisible(false);
        setReviewingMerchant(null); // Reset reviewing merchant
        actionRef.current?.reload();
      }
    }
  };

  // Reset Password Modal Submit Logic
  const handleResetPasswordSubmit = async (values: {
    password_New: string;
  }) => {
    if (
      resettingPasswordMerchant &&
      resettingPasswordMerchant.merchantId !== undefined
    ) {
      const result = await resetMerchantPassword(
        resettingPasswordMerchant.merchantId,
        values,
      );
      if (result.success) {
        setIsResetPasswordModalVisible(false);
        setResettingPasswordMerchant(null);
        // actionRef.current?.reload(); // Password reset might not require immediate table reload
        message.success('密码重置成功');
      }
    } else {
      message.error('商户信息不完整，无法重置密码');
    }
  };

  // 处理创建API Key操作
  const handleOpenCreateApiKeyModal = (
    record: AdminApiApiSystemV1MerchantInfoType,
  ) => {
    setSelectedMerchantForApiKey(record);
    setIsCreateApiKeyModalVisible(true);
  };

  // Create API Key Modal Submit Logic
  const handleCreateApiKeySubmit = async (
    values: PostApiSystemMerchantsMerchantIdApikeysBody,
    merchantId: number,
  ): Promise<AdminApiApiSystemV1GenerateMerchantApiKeyRes | null> => {
    const result = await createMerchantApiKey(merchantId, values);
    if (result) {
      // Modal will show the key, no need to reload table or close modal from here
      // setIsCreateApiKeyModalVisible(false); // Modal handles its own closing after showing key
      // actionRef.current?.reload(); // No direct need to reload list after creating an API key
    }
    return result; // Return result to modal
  };

  // 动态生成操作列
  const columns: ProColumns<AdminApiApiSystemV1MerchantInfoType>[] = [
    ...merchantListColumns.filter((col) => col.key !== 'action'),
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      width: 400, // Adjusted width for new buttons
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {record.status === MerchantStatus.Pending ? (
            <Button
              type="link"
              key="review"
              onClick={() => handleReview(record)}
            >
              审核
            </Button>
          ) : (
            <Button type="link" key="edit" onClick={() => handleEdit(record)}>
              编辑
            </Button>
          )}
          {record.status === MerchantStatus.Active && (
            <Button
              type="link"
              key="assetManagement"
              onClick={() => handleAssetManagement(record)}
            >
              资产管理
            </Button>
          )}
          <Button
            type="link"
            key="resetPassword"
            onClick={() => {
              setResettingPasswordMerchant(record);
              setIsResetPasswordModalVisible(true);
            }}
          >
            重置密码
          </Button>
          {record.google2FaEnabled==1 && (
            <Popconfirm
              title="确认重置此商户的2FA吗？"
              key="reset2fa"
              onConfirm={async () => {
                if (record.merchantId !== undefined) {
                  const result = await resetMerchant2FA(record.merchantId);
                  if (result.success) {
                    actionRef.current?.reload();
                  }
                }
              }}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" key="reset2fa">
                重置2FA
              </Button>
            </Popconfirm>
          )}
          <Popconfirm
            title="确认删除此商户吗？"
            key="delete"
            onConfirm={() => handleDelete(record.merchantId)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" key="delete" style={{ color: 'red' }}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '商户列表管理',
      }}
    >
      <CommonProTable<AdminApiApiSystemV1MerchantInfoType> // Updated generic type
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params, sorter, filter) => {
          // ProTable params are already in MerchantListParams structure from ./types.ts
          // The hook's fetchMerchants will adapt them further for the API.
          //console.log('ProTable request params:', params, sorter, filter);
          return fetchMerchants({ ...params, sorter, filter }); // Use updated fetchMerchants
        }}
        loading={loading}
        rowKey="merchantId" // Updated rowKey
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false, // 默认展开搜索表单
          span: {
            xs: 24, // 小屏幕占满一行
            sm: 12, // 中等屏幕占半行
            md: 8, // 大屏幕占三分之一行
            lg: 6, // 超大屏幕占四分之一行
            xl: 6,
            xxl: 6,
          },
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
          density: true,
          fullScreen: true,
          reload: true,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
        }}
        dateFormatter="string"
        headerTitle="商户列表"
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            type="primary"
          >
            新建商户
          </Button>,
        ]}
      />
      {/* Add/Edit Modal */}
      {(isAddModalVisible || isEditModalVisible) && (
        <MerchantFormModal
          visible={isAddModalVisible || isEditModalVisible}
          onCancel={() => {
            setIsAddModalVisible(false);
            setIsEditModalVisible(false);
            setEditingMerchant(null);
          }}
          onSubmit={handleFormSubmit}
          loading={loading}
          initialValues={editingMerchant || {}}
          isEditMode={!!editingMerchant} // Re-added prop
        />
      )}

      {/* Review Modal */}
      {isReviewModalVisible && reviewingMerchant && (
        <ReviewMerchantModal
          visible={isReviewModalVisible}
          onCancel={() => {
            setIsReviewModalVisible(false);
            setReviewingMerchant(null);
          }}
          onSubmit={handleReviewSubmit}
          merchantInfo={reviewingMerchant}
          loading={loading}
        />
      )}

      {/* Reset Password Modal */}
      {isResetPasswordModalVisible && resettingPasswordMerchant && (
        <ResetPasswordModal
          visible={isResetPasswordModalVisible}
          onCancel={() => {
            setIsResetPasswordModalVisible(false);
            setResettingPasswordMerchant(null);
          }}
          onSubmit={handleResetPasswordSubmit}
          loading={loading}
          merchantName={resettingPasswordMerchant.merchantName}
        />
      )}
      {/* Create API Key Modal */}
      {isCreateApiKeyModalVisible && selectedMerchantForApiKey && (
        <CreateApiKeyModal
          visible={isCreateApiKeyModalVisible}
          merchant={selectedMerchantForApiKey}
          onCreate={handleCreateApiKeySubmit}
          onCancel={() => {
            setIsCreateApiKeyModalVisible(false);
            setSelectedMerchantForApiKey(null);
          }}
        />
      )}
    </PageContainer>
  );
};

export default MerchantListManagement;
