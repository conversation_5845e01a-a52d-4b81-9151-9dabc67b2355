import useI18n from '@/hooks/useI18n';
import { ActionType } from '@ant-design/pro-components';
import { message, Modal } from 'antd';
import { useCallback, useRef, useState } from 'react';
import { useUserBackupColumns } from './components/UserBackupColumns';
import { UserBackupAccount, UseUserBackupManagementResult } from './types';

// Mock Data Store (simulating a database)
let mockData: UserBackupAccount[] = [
  {
    id: 1,
    userId: 101,
    accountType: 'email',
    accountValue: '<EMAIL>',
    isVerified: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    userId: 102,
    accountType: 'phone',
    accountValue: '+**********',
    isVerified: false,
    createdAt: '2024-01-16T11:30:00Z',
    updatedAt: '2024-01-17T09:00:00Z',
  },
  {
    id: 3,
    userId: 101,
    accountType: 'email',
    accountValue: '<EMAIL>',
    isVerified: false,
    createdAt: '2024-02-10T14:00:00Z',
    updatedAt: '2024-02-10T14:00:00Z',
  },
];
let nextId = 4; // Simple ID increment for mock data

/**
 * Custom hook for User Backup Account management operations (using Mock Data)
 */
export const useUserBackupManagement = (): UseUserBackupManagementResult => {
  const actionRef = useRef<ActionType>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { t } = useI18n();
  // Add state for modal if needed later
  // const [modalVisible, setModalVisible] = useState<boolean>(false);
  // const [editingRecord, setEditingRecord] = useState<UserBackupAccount | undefined>(undefined);

  /**
   * Fetch Mock User Backup Accounts
   * Simulates API call with pagination and filtering (basic implementation)
   */
  const fetchMockUserBackups = useCallback(async (params: any = {}) => {
    setLoading(true);
    //console.log('Fetching mock data with params:', params);
    // Simulate network delay
    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });

    // Basic filtering simulation (can be expanded)
    let filteredData = mockData;
    if (params.userId) {
      filteredData = filteredData.filter(
        (item) => item.userId === Number(params.userId),
      );
    }
    if (params.accountType) {
      filteredData = filteredData.filter(
        (item) => item.accountType === params.accountType,
      );
    }
    if (params.accountValue) {
      filteredData = filteredData.filter((item) =>
        item.accountValue.includes(params.accountValue),
      );
    }
    if (params.isVerified !== undefined) {
      filteredData = filteredData.filter(
        (item) => item.isVerified === (params.isVerified === 'true'),
      );
    }

    // Basic sorting simulation
    if (params.sorter) {
      const [field, order] = Object.entries(params.sorter)[0];
      if (field) {
        filteredData.sort((a, b) => {
          const valA = a[field as keyof UserBackupAccount];
          const valB = b[field as keyof UserBackupAccount];
          if (valA < valB) return order === 'ascend' ? -1 : 1;
          if (valA > valB) return order === 'ascend' ? 1 : -1;
          return 0;
        });
      }
    }

    setLoading(false);
    return {
      data: filteredData,
      success: true,
      total: filteredData.length, // In real API, total comes from server
    };
  }, []);

  /**
   * Add Mock User Backup Account
   */
  const addMockUserBackup = useCallback(
    async (
      account: Omit<UserBackupAccount, 'id' | 'createdAt' | 'updatedAt'>,
    ) => {
      setLoading(true);
      await new Promise((resolve) => {
        setTimeout(resolve, 300);
      }); // Simulate delay
      const newAccount: UserBackupAccount = {
        ...account,
        id: nextId++,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      mockData.push(newAccount);
      message.success(
        t('userBackup.add.success', 'Mock: Backup account added successfully'),
      );
      actionRef.current?.reload();
      setLoading(false);
      // Close modal logic would go here
    },
    [t],
  );

  /**
   * Delete Mock User Backup Account
   */
  const deleteMockUserBackup = useCallback(
    async (id: number) => {
      Modal.confirm({
        title: t('userBackup.delete.confirm', 'Confirm Deletion'),
        content: t(
          'userBackup.delete.confirm.message',
          'Are you sure you want to delete backup account ID: {id}?',
          { id },
        ),
        onOk: async () => {
          setLoading(true);
          await new Promise((resolve) => {
            setTimeout(resolve, 300);
          }); // Simulate delay
          mockData = mockData.filter((item) => item.id !== id);
          message.success(
            t(
              'userBackup.delete.success',
              `Mock: Backup account ID: ${id} deleted successfully`,
              { id },
            ),
          );
          actionRef.current?.reload();
          setLoading(false);
        },
      });
    },
    [t],
  );

  // Placeholder handlers for column actions
  const handleEdit = useCallback(
    (record: UserBackupAccount) => {
      //console.log('Edit record:', record);
      message.info(
        t(
          'userBackup.edit.info',
          `Mock: Edit action triggered for ID: ${record.id}. Implement modal logic.`,
          { id: record.id },
        ),
      );
      // setEditingRecord(record);
      // setModalVisible(true);
    },
    [t],
  );

  const handleDelete = useCallback(
    (id: number) => {
      deleteMockUserBackup(id);
    },
    [deleteMockUserBackup],
  );

  // Placeholder modal handlers
  // const showModal = useCallback(() => { /* ... */ }, []);
  // const handleCancel = useCallback(() => { /* ... */ }, []);
  // const handleSubmit = useCallback(() => { /* ... */ }, []);

  // Get columns with memoization
  const columns = useUserBackupColumns(handleEdit, handleDelete);

  return {
    actionRef,
    loading,
    columns,
    fetchMockUserBackups,
    addMockUserBackup, // Provide add function
    deleteMockUserBackup, // Provide delete function
    // Add modal state and handlers if/when implemented
  };
};
