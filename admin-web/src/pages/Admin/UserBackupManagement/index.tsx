import useI18n from '@/hooks/useI18n';
import { PlusOutlined } from '@ant-design/icons';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import React from 'react';
import { useUserBackupManagement } from './hooks';
import { UserBackupAccount } from './types';
// Import modal component if/when created, e.g.:
// import UserBackupFormModal from './components/UserBackupFormModal';

/**
 * User Backup Account Management Page
 * Provides UI for viewing and managing user backup accounts (using Mock Data).
 */
const UserBackupManagement: React.FC = () => {
  const {
    actionRef,
    loading,
    columns,
    fetchMockUserBackups,
    // Add modal state and handlers if/when implemented
    // modalVisible,
    // modalTitle,
    // editingRecord,
    // showModal,
    // handleSubmit,
    // handleCancel,
    addMockUserBackup, // Example usage for a potential add button
  } = useUserBackupManagement();

  const { t } = useI18n();

  // Placeholder function for triggering add modal
  const handleShowAddModal = () => {
    // In a real scenario, this would likely call a 'showModal' function from the hook
    // For now, we can simulate adding a new record directly for demonstration
    //console.log('Triggering add action...');
    // Example of directly calling the mock add function (replace with modal logic)
    addMockUserBackup({
      userId: Math.floor(Math.random() * 100) + 100, // Random user ID
      accountType: Math.random() > 0.5 ? 'email' : 'phone',
      accountValue:
        Math.random() > 0.5
          ? `new_mock_${Date.now()}@test.com`
          : `+${Math.floor(Math.random() * ***********)}`,
      isVerified: Math.random() > 0.5,
    });
    // showModal(); // Call this when modal is implemented
  };

  return (
    <PageContainer
      title={t('userBackup.management.title', 'User Backup Account Management')}
    >
      <ProTable<UserBackupAccount> // Add type parameter for ProTable
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        loading={loading}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={handleShowAddModal} // Connect to placeholder add function
            icon={<PlusOutlined />}
          >
            {t('userBackup.management.addAccount', 'Add Backup Account (Mock)')}
          </Button>,
        ]}
        request={fetchMockUserBackups} // Use the mock fetch function
        columns={columns}
        pagination={{
          showSizeChanger: false,
          showQuickJumper: true,
        }}
      />

      {/* Placeholder for the Add/Edit Modal */}
      {/* <UserBackupFormModal
        title={modalTitle}
        visible={modalVisible}
        initialValues={editingRecord} // Pass editing data to modal
        loading={loading}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      /> */}
    </PageContainer>
  );
};

export default UserBackupManagement;
