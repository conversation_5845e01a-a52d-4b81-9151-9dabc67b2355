import { useRef, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { AUTO_REFRESH_INTERVAL } from '../constants';
import type { RealtimeUpdateConfig, WithdrawRecord } from '../types';

/**
 * 实时更新Hook，用于定期刷新数据
 */
export const useRealtimeUpdates = (
  config: RealtimeUpdateConfig,
  refreshCallback: () => void
) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(true);

  /**
   * 启动实时更新
   */
  const startRealtimeUpdate = useCallback(() => {
    if (!config.enabled || intervalRef.current) return;

    intervalRef.current = setInterval(() => {
      if (isActiveRef.current && document.visibilityState === 'visible') {
        try {
          refreshCallback();
          config.onUpdate?.([]);
        } catch (error) {
          console.error('实时更新失败:', error);
          config.onError?.(error as Error);
        }
      }
    }, config.interval || AUTO_REFRESH_INTERVAL);
  }, [config, refreshCallback]);

  /**
   * 停止实时更新
   */
  const stopRealtimeUpdate = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  /**
   * 切换实时更新状态
   */
  const toggleRealtimeUpdate = useCallback(() => {
    if (intervalRef.current) {
      stopRealtimeUpdate();
    } else {
      startRealtimeUpdate();
    }
  }, [startRealtimeUpdate, stopRealtimeUpdate]);

  /**
   * 处理页面可见性变化
   */
  const handleVisibilityChange = useCallback(() => {
    isActiveRef.current = document.visibilityState === 'visible';
    
    if (isActiveRef.current && config.enabled) {
      // 页面变为可见时，立即刷新一次
      setTimeout(refreshCallback, 100);
    }
  }, [config.enabled, refreshCallback]);

  // 监听页面可见性变化
  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [handleVisibilityChange]);

  // 组件挂载时启动实时更新
  useEffect(() => {
    if (config.enabled) {
      startRealtimeUpdate();
    }

    return () => {
      stopRealtimeUpdate();
    };
  }, [config.enabled, startRealtimeUpdate, stopRealtimeUpdate]);

  return {
    isRunning: !!intervalRef.current,
    startRealtimeUpdate,
    stopRealtimeUpdate,
    toggleRealtimeUpdate,
  };
};

/**
 * WebSocket实时更新Hook（预留接口）
 */
export const useWebSocketUpdates = (
  url: string,
  onMessage: (data: any) => void,
  onError?: (error: Event) => void
) => {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  /**
   * 连接WebSocket
   */
  const connect = useCallback(() => {
    try {
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        //console.log('WebSocket连接已建立');
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      wsRef.current.onclose = () => {
        //console.log('WebSocket连接已关闭');
        
        // 尝试重连
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            //console.log(`尝试第${reconnectAttempts.current}次重连`);
            connect();
          }, 3000 * reconnectAttempts.current);
        } else {
          message.error('WebSocket连接失败，请刷新页面重试');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        onError?.(error);
      };
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      onError?.(error as Event);
    }
  }, [url, onMessage, onError]);

  /**
   * 断开WebSocket连接
   */
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, []);

  /**
   * 发送消息
   */
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }, []);

  // 组件挂载时连接
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    isConnected: wsRef.current?.readyState === WebSocket.OPEN,
    connect,
    disconnect,
    sendMessage,
  };
};