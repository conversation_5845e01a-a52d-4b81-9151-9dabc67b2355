import { Form, message } from 'antd';
import { useState } from 'react';
import type { MerchantFormData } from './types'; // Assuming types.ts will define this

// Define the return type for the hook if needed, similar to MenuManagement
// export interface UseMerchantCreateFormResult { ... }

/**
 * Custom hook for merchant creation form management
 */
export const useMerchantCreateForm = () => {
  const [form] = Form.useForm<MerchantFormData>();
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * Mock function to simulate creating a merchant
   * @param data - The merchant form data
   */
  const createMockMerchant = async (
    data: MerchantFormData,
  ): Promise<{ success: boolean; message: string }> => {
    //console.log('Simulating merchant creation with data:', data);
    setLoading(true);
    // Simulate API call delay
    await new Promise((resolve) => {
      setTimeout(resolve, 1500);
    });
    setLoading(false);

    // Simulate success/failure randomly or based on specific conditions
    const isSuccess = Math.random() > 0.2; // 80% success rate for demo

    if (isSuccess) {
      //console.log('Mock merchant creation successful');
      return { success: true, message: '商户创建成功 (模拟)' };
    } else {
      console.error('Mock merchant creation failed');
      return { success: false, message: '商户创建失败 (模拟)' };
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true); // Ensure loading is true before async call

      const result = await createMockMerchant(values);

      if (result.success) {
        message.success(result.message);
        // Optional: Reset form or redirect after successful creation
        // form.resetFields();
        // navigate('/admin/merchant/list'); // Example redirect
      } else {
        message.error(result.message);
      }
    } catch (errorInfo) {
      // Antd form validation errors are caught here
      //console.log('Form validation failed:', errorInfo);
      message.error('请检查表单输入项！');
      // Ensure loading is false even if validation fails before async call
      setLoading(false);
    }
    // setLoading(false); // setLoading is handled within createMockMerchant now
  };

  return {
    form,
    loading,
    handleSubmit,
    // You might not need createMockMerchant directly in the component,
    // but it's here for clarity or potential direct use if needed.
    // createMockMerchant
  };
};
