import { getSystemAddress } from '@/api/interfaces/system-address/system-address';
import { getSystemCommon } from '@/api/interfaces/system-common/system-common';
import { getSystemUserAddress } from '@/api/interfaces/system-user-address/system-user-address';
import type { ActionType } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import type {
  AddressFormValues,
  AddressStatisticsItem,
  FetchRechargeAddressesParams,
  ImportProgressItem,
  RechargeAddressItem,
} from './types';
import { TaskStatus } from './types';

/**
 * 用于管理充值地址页面逻辑的自定义钩子
 */
export const useFundRechargeAddressesManagement = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] =
    useState<Partial<AddressFormValues> | null>(null);
  const [modalTitle, setModalTitle] = useState('添加充值地址');

  // QR Code modal state
  const [qrCodeModalVisible, setQrCodeModalVisible] = useState(false);
  const [currentQRCode, setCurrentQRCode] = useState<string | undefined>();
  const [qrCodeTitle, setQrCodeTitle] = useState('充值地址二维码');

  // Address statistics state
  const [addressStats, setAddressStats] = useState<AddressStatisticsItem[]>([]);
  const [statsLoading, setStatsLoading] = useState(false);

  // Import address state
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [progressInfo, setProgressInfo] = useState<
    ImportProgressItem | undefined
  >();
  const [progressLoading, setProgressLoading] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | undefined>();
  const [progressCheckInterval, setProgressCheckInterval] =
    useState<NodeJS.Timeout | null>(null);

  // 获取币种符号列表
  const { data: tokenSymbolsData, loading: tokenSymbolsLoading } = useRequest(
    async () => {
      try {
        const response = await getSystemCommon().getApiSystemTokensSymbols();
        if (response.symbols) {
          return response.symbols.filter(Boolean);
        }
        return [];
      } catch (error) {
        console.error('Failed to fetch token symbols:', error);
        return [];
      }
    },
    {
      cacheKey: 'token-symbols',
    },
  );

  /**
   * 获取充值地址列表
   */
  const fetchRechargeAddresses = async (
    params: FetchRechargeAddressesParams,
    _sort?: Record<string, any>,
    _filter?: Record<string, any>,
  ) => {
    setLoading(true);
    try {
      // 转换参数以适配API
      const { dateRange, current, pageSize, name, type, ...restParams } =
        params;

      const apiParams = {
        ...restParams,
        page: current,
        pageSize,
        symbol: name, // 币种名称参数适配
        type, // 地址类型参数适配
        // 直接传递dateRange字符串，格式为 "YYYY-MM-DD,YYYY-MM-DD"
        dateRange: dateRange || undefined,
      };

      // 调用API获取数据
      const response = await getSystemUserAddress().getApiSystemUserAddresses(
        apiParams,
      );

      // 确保每个记录的status字段是有效值（0或1）
      const processedData = (response.data || []).map((item) => {
        const apiItem = item as any; // 允许访问可能不存在的属性
        return {
          ...apiItem,
          userAddressId: apiItem.userAddressId || 0, // 确保userAddressId是数字
          address: apiItem.address || '', // 确保address是字符串
          status: apiItem.status === 1 ? 1 : 0, // 确保status是0或1，如果API没有返回status，则默认为0
        };
      }) as RechargeAddressItem[];

      setLoading(false);

      return {
        data: processedData,
        success: true,
        total: response.page?.totalSize || 0,
      };
    } catch (error) {
      console.error('获取充值地址列表失败:', error);
      setLoading(false);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  /**
   * 处理添加新地址
   */
  const handleAddAddress = async (values: AddressFormValues) => {
    setLoading(true);
    try {
      // 这里应该调用添加地址的API
      // 由于API文档中没有提供添加地址的接口，这里仅做模拟
      //console.log('添加地址:', values);

      // 模拟API调用延迟
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 500);
      });

      message.success('地址添加成功');
      setModalVisible(false);
      actionRef.current?.reload();
      return true;
    } catch (error) {
      console.error('添加地址失败:', error);
      message.error('添加地址失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理编辑地址
   */
  const handleEditAddress = async (values: AddressFormValues) => {
    setLoading(true);
    try {
      // 这里应该调用编辑地址的API
      // 由于API文档中没有提供编辑地址的接口，这里仅做模拟
      //console.log('编辑地址:', values);

      // 模拟API调用延迟
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 500);
      });

      message.success('地址编辑成功');
      setModalVisible(false);
      actionRef.current?.reload();
      return true;
    } catch (error) {
      console.error('编辑地址失败:', error);
      message.error('编辑地址失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理启用/禁用地址
   */
  const handleToggleStatus = async (id: number, currentStatus: number) => {
    setLoading(true);
    try {
      const newStatus = currentStatus === 1 ? 0 : 1;
      //console.log(`切换ID为${id}的地址状态为${newStatus}`);

      // 这里应该调用更新地址状态的API
      // 由于API文档中没有提供更新地址状态的接口，这里仅做模拟
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 300);
      });

      message.success(`地址状态已${newStatus === 1 ? '启用' : '禁用'}`);
      actionRef.current?.reload();
      return true;
    } catch (error) {
      console.error('更新地址状态失败:', error);
      message.error('更新地址状态失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 显示添加模态框
   */
  const showAddModal = () => {
    setEditingAddress(null);
    setModalTitle('添加充值地址');
    setModalVisible(true);
  };

  /**
   * 显示编辑模态框
   */
  const showEditModal = (record: RechargeAddressItem) => {
    setEditingAddress({
      tokenId: record.tokenId,
      userId: record.userId,
      account: record.account,
      username: record.username,
      chan: record.chan || '',
      address: record.address,
      lable: record.lable,
      type: record.type,
      status: record.status,
    });
    setModalTitle('编辑充值地址');
    setModalVisible(true);
  };

  /**
   * 处理取消模态框
   */
  const handleCancel = () => {
    setModalVisible(false);
  };

  /**
   * 处理提交表单
   */
  const handleSubmit = async (values: AddressFormValues) => {
    if (editingAddress) {
      return handleEditAddress(values);
    } else {
      return handleAddAddress(values);
    }
  };

  /**
   * 显示二维码模态框
   */
  const showQRCodeModal = (record: RechargeAddressItem) => {
    setCurrentQRCode(record.image);
    setQrCodeTitle(`${record.name || ''} 充值地址二维码`);
    setQrCodeModalVisible(true);
  };

  /**
   * 关闭二维码模态框
   */
  const closeQRCodeModal = () => {
    setQrCodeModalVisible(false);
  };

  /**
   * 获取地址统计信息
   */
  const fetchAddressStatistics = async () => {
    setStatsLoading(true);
    try {
      const response = await getSystemAddress().getApiSystemAddressStatistics();
      if (response.data) {
        setAddressStats(response.data);
      }
    } catch (error) {
      console.error('获取地址统计信息失败:', error);
      message.error('获取地址统计信息失败');
    } finally {
      setStatsLoading(false);
    }
  };

  /**
   * 显示导入地址模态框
   */
  const showImportModal = () => {
    setImportModalVisible(true);
  };

  /**
   * 关闭导入地址模态框
   */
  const closeImportModal = () => {
    // 如果有正在进行的进度检查，且任务不是已完成或失败状态，则不关闭模态框
    if (
      progressLoading &&
      currentTaskId &&
      progressInfo?.status !== TaskStatus.Completed &&
      progressInfo?.status !== TaskStatus.Failed
    ) {
      message.warning('导入任务正在进行中，请等待完成');
      return;
    }

    // 清除进度检查定时器
    if (progressCheckInterval) {
      clearInterval(progressCheckInterval);
      setProgressCheckInterval(null);
    }

    setImportModalVisible(false);
    setProgressInfo(undefined);
    setCurrentTaskId(undefined);
    setProgressLoading(false); // 确保关闭模态框时重置加载状态
  };

  /**
   * 检查导入进度
   */
  const checkImportProgress = async (taskId: string) => {
    if (!taskId) return;

    setProgressLoading(true);
    try {
      const response =
        await getSystemAddress().getApiSystemAddressesImportTaskIdProgress(
          taskId,
        );

      //console.log('导入进度:', response.data);

      if (response.data) {
        setProgressInfo(response.data);

        // 如果导入已完成或失败，停止检查
        if (
          response.data?.status === TaskStatus.Completed ||
          response.data?.status === TaskStatus.Failed
        ) {
          // 停止轮询
          if (progressCheckInterval) {
            clearInterval(progressCheckInterval);
            setProgressCheckInterval(null);
          }
          setProgressLoading(false); // 确保在任务完成或失败时停止加载状态

          // 如果导入成功，刷新地址统计信息
          if (response.data.status === TaskStatus.Completed) {
            message.success('地址导入成功');
            fetchAddressStatistics();
          } else if (response.data?.status === TaskStatus.Failed) {
            message.error(
              `地址导入失败: ${response.data?.errorMessage || '未知错误'}`,
            );
          }

          return; // 任务完成或失败后直接返回，不继续执行
        }
      }

      // 如果任务仍在进行中，则继续检查
      if (
        response.data?.status !== TaskStatus.Completed &&
        response.data?.status !== TaskStatus.Failed
      ) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        startProgressCheck(taskId);
      }
    } catch (error) {
      console.error('获取导入进度失败:', error);

      // 如果获取进度失败，停止检查
      if (progressCheckInterval) {
        clearInterval(progressCheckInterval);
        setProgressCheckInterval(null);
      }
    } finally {
      // 只有当任务仍在进行中时，才在finally中设置setProgressLoading(false)
      // 如果任务已完成或失败，则在相应的分支中处理
      if (
        progressInfo?.status !== TaskStatus.Completed &&
        progressInfo?.status !== TaskStatus.Failed
      ) {
        setProgressLoading(false);
      }
    }
  };

  /**
   * 开始检查导入进度
   */
  const startProgressCheck = async (taskId: string) => {
    // 清除之前的定时器
    if (progressCheckInterval) {
      clearInterval(progressCheckInterval);
      setProgressCheckInterval(null);
    }

    //延时一秒查询
    await new Promise<void>((resolve) => {
      setTimeout(resolve, 1000);
    });
    await checkImportProgress(taskId);
  };

  /**
   * 上传CSV文件
   */
  const handleUploadFile = async (file: File) => {
    setImportLoading(true);
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);

      // 调用导入地址API
      const response = await getSystemAddress().postApiSystemAddressesImport({
        file,
      });

      if (response.taskId) {
        setCurrentTaskId(response.taskId);
        message.success('文件上传成功，开始导入处理');

        // 开始检查进度
        await startProgressCheck(response.taskId);
        return response.taskId;
      } else {
        message.error('上传失败，未获取到任务ID');
      }
    } catch (error) {
      console.error('上传文件失败:', error);
      message.error('上传文件失败');
    } finally {
      setImportLoading(false);
    }
    return undefined;
  };

  // 组件挂载时获取地址统计信息
  useEffect(() => {
    fetchAddressStatistics();

    // 组件卸载时清除定时器
    return () => {
      if (progressCheckInterval) {
        clearInterval(progressCheckInterval);
      }
    };
  }, []);

  return {
    actionRef,
    loading,
    fetchRechargeAddresses,
    handleToggleStatus,
    modalVisible,
    modalTitle,
    editingAddress,
    showAddModal,
    showEditModal,
    handleCancel,
    handleSubmit,
    // Token symbols related
    tokenSymbols: tokenSymbolsData || [],
    tokenSymbolsLoading,
    // QR Code related
    qrCodeModalVisible,
    currentQRCode,
    qrCodeTitle,
    showQRCodeModal,
    closeQRCodeModal,
    // Address statistics related
    addressStats,
    statsLoading,
    fetchAddressStatistics,
    // Import address related
    importModalVisible,
    importLoading,
    progressInfo,
    progressLoading,
    showImportModal,
    closeImportModal,
    handleUploadFile,
  };
};
