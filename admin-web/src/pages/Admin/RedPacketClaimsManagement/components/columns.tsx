import { useUnifiedFields } from '@/components/UnifiedFields';
import type { ProColumns } from '@ant-design/pro-components';
import { Badge, Tag } from 'antd';
import { STATUS_ENUM, TYPE_ENUM, TYPE_OPTIONS } from '../constants';
import type { RedPacketClaimsItem } from '../types';
import { formatAmount, formatDateTime } from '../utils';

/**
 * 获取红包类型标签的辅助函数
 */
const getTypeTag = (type?: string) => {
  const option = TYPE_OPTIONS.find((item) => item.value === type);
  if (!option) {
    return <Tag>未知</Tag>;
  }
  return <Tag color={option.color}>{option.label}</Tag>;
};

/**
 * 获取基础红包领取记录表格列（不包含统一搜索字段）
 */
export const getBaseRedPacketClaimsColumns = (
  tokenSymbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<RedPacketClaimsItem>[] => {
  return [
    {
      title: '领取ID',
      dataIndex: 'claimId',
      key: 'claimId',
      width: 80,
      search: false,
    },
    {
      title: '红包ID',
      dataIndex: 'redPacketId',
      key: 'redPacketId',
      width: 80,
      render: (text) => text || '--',
    },
    {
      title: '红包UUID',
      dataIndex: 'redPacketUuid',
      key: 'redPacketUuid',
      width: 200,
      ellipsis: true,
      copyable: true,
      fieldProps: {
        placeholder: '请输入红包UUID',
      },
      render: (text) => text || '--',
    },
    // {
    //   title: '领取方用户名',
    //   dataIndex: 'telegramUsername',
    //   key: 'telegramUsername',
    //   width: 120,
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },

    {
      title: '代币符号',
      dataIndex: 'tokenSymbol',
      key: 'tokenSymbol',
      width: 100,
      valueType: 'select',
      fieldProps: {
        loading: tokenLoading,
        options: tokenSymbols.map((symbol) => ({
          label: symbol,
          value: symbol,
        })),
        placeholder: '请选择代币',
        allowClear: true,
      },
      render: (text) => <Tag color="blue">{text || '--'}</Tag>,
    },
    // {
    //   title: '代币名称',
    //   dataIndex: 'tokenName',
    //   key: 'tokenName',
    //   width: 120,
    //   search: false,
    //   ellipsis: true,
    //   render: (text) => text || '--',
    // },
    {
      title: '领取金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      search: false,
      render: (text, record) =>
        formatAmount(Number(text), record.tokenSymbol || record.symbol),
    },

    {
      title: '交易ID',
      dataIndex: 'transactionId',
      key: 'transactionId',
      width: 100,
      search: false,
      render: (text) => text || '--',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: STATUS_ENUM,
      render: (text, record) => {
        // //console.log('Status render - text:', text, 'record.status:', record.status);
        const statusValue = record.status;
        // //console.log('Using statusValue:', statusValue, 'type:', typeof statusValue);
        const status = STATUS_ENUM[statusValue as keyof typeof STATUS_ENUM];
        // //console.log('Found status:', status);
        if (!status) {
          // //console.log('Status not found, returning 未知');
          return <Badge status="default" text="未知" />;
        }
        // //console.log('Returning status:', status.text);
        return <Badge status={status.status as any} text={status.text} />;
      },
    },
    {
      title: '红包类型',
      dataIndex: 'redPacketType',
      key: 'redPacketType',
      width: 100,
      valueType: 'select',
      valueEnum: TYPE_ENUM,
      fieldProps: {
        options: TYPE_OPTIONS.map((item) => ({
          label: item.label,
          value: item.value,
        })),
        placeholder: '请选择红包类型',
        allowClear: true,
      },
      render: (_, record) => getTypeTag(record.redPacketType),
    },
    {
      title: '红包备注',
      dataIndex: 'redPacketMemo',
      key: 'redPacketMemo',
      ellipsis: true,
      width: 100,
      search: false,
      render: (text) => text || '--',
    },

    {
      title: '领取时间',
      dataIndex: 'claimedAt',
      key: 'claimedAt',
      width: 160,
      hideInSearch: true,
      render: (text) => (text ? formatDateTime(String(text)) : '--'),
    },
    {
      title: '发送方姓名',
      dataIndex: 'senderFirstName',
      key: 'senderFirstName',
      width: 120,
      search: false,
      ellipsis: true,
      render: (text) => text || '--',
    },
    {
      title: '发送方Telegram',
      dataIndex: 'senderTelegramUsername',
      key: 'senderTelegramUsername',
      width: 150,
      search: false,
      ellipsis: true,
      render: (text) => text || '--',
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'createdAt',
    //   key: 'createdAt',
    //   width: 160,
    //   hideInSearch: true,
    //   render: (text) => formatDateTime(text),
    // },
  ];
};

/**
 * 获取带有统一搜索字段的完整红包领取记录表格列
 */
export const getRedPacketClaimsColumns = (
  tokenSymbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<RedPacketClaimsItem>[] => {
  const baseColumns = getBaseRedPacketClaimsColumns(tokenSymbols, tokenLoading);

  return useUnifiedFields(
    baseColumns,
    {
      dateRange: { search: true, display: false },
      firstAgent: { search: true, display: true },
      secondAgent: { search: true, display: true },
      thirdAgent: { search: true, display: true },
      telegramId: { search: true, display: true },
      telegramUsername: { search: true, display: true },
      firstName: { search: true, display: true },
    },
    undefined,
    3, // 在领取ID、红包ID、红包UUID、领取方用户名之后插入统一字段
  );
};
