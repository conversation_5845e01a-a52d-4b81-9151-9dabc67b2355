// src/pages/Admin/FundRechargesManagement/hooks.ts
import { getSystemCommon } from '@/api/interfaces/system-common/system-common';
import { getSystemUserRecharges } from '@/api/interfaces/system-user-recharges/system-user-recharges';
import { ActionType } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useCallback, useRef, useState } from 'react';
import {
  FundRechargeItem,
  GetFundRechargesParams,
  ReviewRechargeParams,
} from './types';
import { formatRequestParams } from './utils';

/**
 * 获取代币符号列表的自定义Hook
 */
export const useTokenSymbols = () => {
  const { data, loading } = useRequest(
    async () => {
      try {
        const response = await getSystemCommon().getApiSystemTokensSymbols();
        return [...new Set((response.symbols || []).map((item) => item))];
      } catch (error) {
        console.error('获取代币符号列表失败:', error);
        return [];
      }
    },
    {
      cacheKey: 'token-symbols',
    },
  );

  return {
    tokenSymbols: data || [],
    loading,
  };
};

/**
 * FundRechargesManagement 页面的自定义 Hook
 */
export const useFundRecharges = () => {
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<FundRechargeItem | null>(
    null,
  );
  const actionRef = useRef<ActionType>();
  const { tokenSymbols, loading: tokenLoading } = useTokenSymbols();

  // 获取充值记录数据
  const fetchData = useCallback(
    async (
      params: GetFundRechargesParams & { pageSize?: number; current?: number },
    ) => {
      setLoading(true);
      try {
        // 格式化请求参数
        const apiParams = formatRequestParams(params);

        // 调用API获取数据
        const response =
          await getSystemUserRecharges().getApiSystemUserRecharges(apiParams);

        // 处理API响应
        const { page, data } = response;

        // 将API响应数据转换为FundRechargeItem类型
        const formattedData = (data || []).map((item) => ({
          ...item,
          // 添加getter属性不会在这里生效，所以我们直接添加别名属性
          status: item.state,
          currency: item.name,
          transactionId: item.txHash,
          remark: item.error,
          id: item.userRechargesId,
        })) as FundRechargeItem[];

        setLoading(false);
        return {
          data: formattedData,
          success: true,
          total: page?.totalSize || 0,
        };
      } catch (error) {
        console.error('获取充值记录失败:', error);
        message.error('获取充值记录失败');
        setLoading(false);
        return { data: [], success: false, total: 0 };
      }
    },
    [],
  );

  // 处理查看详情 - 打开抽屉显示详情
  const handleViewDetails = useCallback((record: FundRechargeItem) => {
    //console.log('查看详情:', record);
    setSelectedRecord(record);
    setDrawerVisible(true);
  }, []);

  // 关闭详情抽屉
  const handleCloseDrawer = useCallback(() => {
    setDrawerVisible(false);
  }, []);

  // 处理审核操作 - 目前API中没有审核功能，保留此函数以便将来实现
  const handleReviewRecharge = useCallback(
    async (params: ReviewRechargeParams) => {
      setLoading(true);
      try {
        // 这里应该调用审核API，但目前API中没有此功能
        // 临时实现，仅显示消息
        //console.log('审核充值记录:', params);
        message.info('审核功能尚未实现');

        // 刷新表格数据
        actionRef.current?.reload();
      } catch (error) {
        console.error('审核充值记录失败:', error);
        message.error('审核操作失败');
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  return {
    loading,
    tokenLoading,
    tokenSymbols,
    fetchData,
    handleReviewRecharge,
    handleViewDetails,
    handleCloseDrawer,
    drawerVisible,
    selectedRecord,
    actionRef,
  };
};
