import { Pie } from '@ant-design/plots';
import { Card, Spin } from 'antd';
import React from 'react';
import {
  TRANSACTION_STATUS_COLORS,
  TRANSACTION_STATUS_MAP,
} from '../constants';
import { TransactionStatusData } from '../types';

interface TransactionStatusChartProps {
  data: TransactionStatusData;
  loading?: boolean;
}

/**
 * 交易状态饼图组件
 */
const TransactionStatusChart: React.FC<TransactionStatusChartProps> = ({
  data,
  loading = false,
}) => {
  // Log the incoming data prop
  //console.log('TransactionStatusChart data prop:', data);

  // 转换数据为饼图所需格式
  const chartData = [
    { type: TRANSACTION_STATUS_MAP.successful, value: data.successful },
    { type: TRANSACTION_STATUS_MAP.pending, value: data.pending },
    { type: TRANSACTION_STATUS_MAP.failed, value: data.failed },
    { type: TRANSACTION_STATUS_MAP.refunded, value: data.refunded },
  ];

  // Log the transformed chartData
  //console.log('TransactionStatusChart chartData:', chartData);

  const config = {
    appendPadding: 10,
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.75,
    legend: {
      position: 'right',
      layout: 'vertical',
    },
    label: {
      type: 'spider',
      labelHeight: 28,
      content: '{name}\n{percentage}',
    },
    interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
    color: ({ type }: { type: string }) => {
      const statusKey = Object.keys(TRANSACTION_STATUS_MAP).find(
        (key) =>
          TRANSACTION_STATUS_MAP[key as keyof typeof TRANSACTION_STATUS_MAP] ===
          type,
      );
      return statusKey
        ? TRANSACTION_STATUS_COLORS[
            statusKey as keyof typeof TRANSACTION_STATUS_COLORS
          ]
        : '#d9d9d9';
    },
  };

  return (
    <Card title="交易状态分布" variant="borderless">
      {loading ? (
        <div
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Spin />
        </div>
      ) : (
        <div style={{ height: 300 }}>
          <Pie {...config} />
        </div>
      )}
    </Card>
  );
};

export default TransactionStatusChart;
