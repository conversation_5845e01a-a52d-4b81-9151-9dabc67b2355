import { useUnifiedFields } from '@/components/UnifiedFields';
import { ProColumns } from '@ant-design/pro-components';
import { DatePicker, Tag } from 'antd';
import { FundRecord } from './types';
import { formatCurrency, formatDateTime } from './utils'; // 假设utils中有这些函数

const { RangePicker } = DatePicker;

/**
 * 资金记录类型映射（用于显示）
 */
export const FUND_RECORD_TYPE_MAP: Record<FundRecord['type'], string> = {
  deposit: '充值',
  withdrawal: '提现',
  transfer_in: '转入',
  transfer_out: '转出',
  commission: '佣金',
  other: '其他',
};

/**
 * API交易类型映射（用于显示）
 */
export const API_TRANSACTION_TYPE_MAP: Record<string, string> = {
  ...FUND_RECORD_TYPE_MAP,
  red_packet_create: '创建红包',
  red_packet_claim: '领取红包',
  withdraw: '提现',
};

/**
 * 资金记录状态映射及颜色（用于显示）
 */
export const FUND_RECORD_STATUS_MAP: Record<
  FundRecord['status'],
  { text: string; color: string }
> = {
  pending: { text: '待处理', color: 'processing' },
  completed: { text: '已完成', color: 'success' },
  failed: { text: '失败', color: 'error' },
  cancelled: { text: '已取消', color: 'default' },
};

/**
 * 资金方向映射
 */
export const FUND_DIRECTION_MAP: Record<
  string,
  { text: string; color: string }
> = {
  in: { text: '收入', color: 'green' },
  out: { text: '支出', color: 'red' },
};

/**
 * 资金记录表格列定义
 * @param tokenSymbols 代币符号列表，用于货币筛选下拉框
 */
export const getFundRecordColumns = (
  tokenSymbols: string[] = [],
): ProColumns<FundRecord>[] => {
  const baseColumns: ProColumns<FundRecord>[] = [
    {
      title: '记录ID',
      dataIndex: 'id',
      key: 'id',
      copyable: true,
      ellipsis: true,
      width: 150,
      fixed: 'left', // 固定在左侧
      search: false, // 通常不会按确切ID在UI中搜索
    },
    {
      title: '交易ID',
      dataIndex: 'transactionId',
      key: 'transactionId',
      copyable: true,
      ellipsis: true,
      width: 150,
      hideInTable: true, // 在表格中隐藏，但在搜索表单中显示
    },
    {
      title: '关联交易ID',
      dataIndex: 'relatedTransactionId',
      key: 'relatedTransactionId',
      width: 150,
      hideInTable: true, // 在表格中隐藏，但在搜索表单中显示
    },
    // {
    //   title: '用户名',
    //   dataIndex: 'username',
    //   key: 'username',
    //   width: 120,
    // },
    // {
    //   title: '用户ID',
    //   dataIndex: 'userId',
    //   key: 'userId',
    //   width: 100,
    //   hideInTable: true, // 在表格中隐藏，但在搜索表单中显示
    // },
    // {
    //   title: '账号',
    //   dataIndex: 'account',
    //   key: 'account',
    //   width: 120,
    //   hideInTable: true, // 在表格中隐藏，但在搜索表单中显示
    // },
    // {
    //   title: '类型',
    //   dataIndex: 'type',
    //   key: 'type',
    //   width: 120,
    //   // 使用API_TRANSACTION_TYPE_MAP进行筛选/显示
    //   valueEnum: API_TRANSACTION_TYPE_MAP,
    //   render: (_, record) => {
    //     // 使用API_TRANSACTION_TYPE_MAP来显示更多类型
    //     const originalType = record.originalType || record.type;
    //     return (
    //       API_TRANSACTION_TYPE_MAP[originalType] ||
    //       FUND_RECORD_TYPE_MAP[record.type] ||
    //       originalType
    //     );
    //   },
    // },
    {
      title: '方向',
      dataIndex: 'direction',
      key: 'direction',
      width: 80,
      fixed: 'left', // 固定在左侧
      valueEnum: {
        in: '收入',
        out: '支出',
      },
      render: (_, record) => {
        const directionInfo = FUND_DIRECTION_MAP[record.direction || ''] || {
          text: '未知',
          color: 'default',
        };
        return <Tag color={directionInfo.color}>{directionInfo.text}</Tag>;
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      width: 120,
      fixed: 'left', // 固定在左侧
      search: false,
      render: (amount, _record) => Number(amount), // 使用格式化函数
    },
    {
      title: '货币',
      dataIndex: 'currency',
      key: 'currency',
      width: 80,
      fixed: 'left', // 固定在左侧
      valueEnum:
        tokenSymbols.length > 0
          ? tokenSymbols.reduce((acc, symbol) => {
              acc[symbol] = symbol;
              return acc;
            }, {} as Record<string, string>)
          : undefined,
      fieldProps: {
        showSearch: true,
        allowClear: true,
        placeholder: '请选择货币',
      },
    },
    {
      title: '交易前余额',
      dataIndex: 'balanceBefore',
      key: 'balanceBefore',
      align: 'right',
      width: 140,
      search: false,
      render: (balance, record) =>
        formatCurrency(Number(balance), record.currency), // 使用格式化函数
    },
    {
      title: '交易后余额',
      dataIndex: 'balanceAfter',
      key: 'balanceAfter',
      align: 'right',
      width: 140,
      search: false,
      render: (balance, record) =>
        formatCurrency(Number(balance), record.currency), // 使用格式化函数
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   width: 100,
    //   valueEnum: Object.keys(FUND_RECORD_STATUS_MAP).reduce((acc, key) => {
    //     acc[key] = FUND_RECORD_STATUS_MAP[key as FundRecord['status']].text;
    //     return acc;
    //   }, {} as Record<string, string>),
    //   render: (_, record) => {
    //     const statusInfo = FUND_RECORD_STATUS_MAP[record.status];
    //     return (
    //       <Tag color={statusInfo?.color}>
    //         {statusInfo?.text || record.status}
    //       </Tag>
    //     );
    //   },
    // },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      search: false,
      width: 150,
    },
    {
      title: '时间范围',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      hideInTable: true,
      renderFormItem: () => (
        <RangePicker placeholder={['开始日期', '结束日期']} />
      ),
      search: {
        transform: (value) => {
          if (!value || !Array.isArray(value) || value.length !== 2) return {};
          const [start, end] = value;
          if (!start || !end) return {};
          // Ensure we have dayjs objects and handle both dayjs and moment formats
          const startDate = start.format ? start.format('YYYY-MM-DD') : start;
          const endDate = end.format ? end.format('YYYY-MM-DD') : end;
          return {
            createdAt: [startDate, endDate],
          };
        },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      hideInSearch: true,
      render: (_, record) => formatDateTime(record.createdAt), // 使用格式化函数
    },
    // 如果需要，添加操作列，例如查看详情
    // {
    //   title: '操作',
    //   key: 'action',
    //   valueType: 'option',
    //   width: 100,
    //   render: (_, record) => [
    //     <a key="view" onClick={() => //console.log('查看详情:', record.id)}>查看</a>,
    //   ],
    // },
  ];

  // 使用 useUnifiedFields 添加额外的搜索和展示字段
  return useUnifiedFields(
    baseColumns as any,
    {
      firstAgent: { search: true, display: true }, // 一级代理
      secondAgent: { search: true, display: true }, // 二级代理
      thirdAgent: { search: true, display: true }, // 三级代理
      telegramId: { search: true, display: true }, // Telegram ID
      telegramUsername: { search: true, display: true }, // Telegram 用户名
      firstName: { search: true, display: true }, // First Name
    },
    undefined,
    0,
  ) as any; // 在用户名之后插入统一字段
};
