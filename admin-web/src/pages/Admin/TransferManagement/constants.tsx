import { useUnifiedFields } from '@/components/UnifiedFields';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, Tag, Typography } from 'antd';
import { ExtendedTransferItem } from './types';

const { Text } = Typography;

/**
 * 转账表格默认页面大小
 */
export const DEFAULT_PAGE_SIZE = 10;

/**
 * 默认页码
 */
export const DEFAULT_PAGE = 1;

/**
 * 日期显示格式
 */
export const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

/**
 * API请求的日期格式
 */
export const API_DATE_FORMAT = 'YYYY-MM-DD';

/**
 * 日期选择器占位符
 */
export const DATE_RANGE_PLACEHOLDERS = ['开始日期', '结束日期'];

/**
 * 表格默认分页配置
 */
export const DEFAULT_PAGINATION = {
  defaultPageSize: 10,
  showSizeChanger: false,
};

/**
 * 表格默认滚动配置
 */
export const TABLE_SCROLL = { x: 1500 };

/**
 * 状态选项
 */
export const STATUS_OPTIONS = [
  { label: '等待输入支付密码', value: 'pending_pass' },
  { label: '等待领取', value: 'pending_collection' },
  { label: '领取完成', value: 'completed' },
  { label: '已过期', value: 'expired' },
];

/**
 * 获取交易状态标签
 */
export const getStatusTag = (status?: any) => {
  //console.log('getStatusTag input:', status, 'type:', typeof status);

  if (!status && status !== 0) return <Tag>未知</Tag>;

  // 处理可能的对象类型状态值
  let statusValue = status;
  if (typeof status === 'object') {
    statusValue = status.value || status.status || status.label || String(status);
  }

  const statusStr = String(statusValue).toLowerCase();
  //console.log('getStatusTag processing statusStr:', statusStr);

  switch (statusStr) {
    case 'pending_pass':
      return <Tag color="blue">等待输入支付密码</Tag>;
    case 'pending_collection':
      return <Tag color="orange">等待领取</Tag>;
    case 'completed':
      return <Tag color="green">领取完成</Tag>;
    case 'expired':
      return <Tag color="red">已过期</Tag>;
    default:
      return <Tag>{String(statusValue)}</Tag>;
  }
};

/**
 * 获取基础表格列（不包含统一搜索字段）
 */
export const getBaseTransferColumns = (
  viewDetail: (transferId: number) => void,
  tokenSymbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<ExtendedTransferItem>[] => [
  {
    title: '订单号',
    dataIndex: 'key',
    key: 'key',
    width: 200,
    copyable: true,
    search: {
      transform: (value) => ({ key: value }),
    },
  },
  // {
  //   title: '发送方ID',
  //   dataIndex: 'senderUserId',
  //   key: 'senderUserId',
  //   width: 80,
  //   hideInSearch: true,
  // },
  // {
  //   title: '发送方账户',
  //   dataIndex: 'senderAccount',
  //   key: 'senderAccount',
  //   width: 120,
  //   search: {
  //     transform: (value) => ({ senderAccount: value }),
  //   },
  // },
  // {
  //   title: '发送方',
  //   dataIndex: 'senderUsername',
  //   key: 'senderUsername',
  //   width: 120,
  //   search: {
  //     transform: (value) => ({ senderUsername: value }),
  //   },
  // },
  // {
  //   title: '接收方ID',
  //   dataIndex: 'receiverUserId',
  //   key: 'receiverUserId',
  //   width: 80,
  //   render: (text) => (text !== null && text !== undefined ? text : '--'),
  //   hideInSearch: true,
  // },
  {
    title: '接收方账户',
    dataIndex: 'receiverAccount',
    key: 'receiverAccount',
    width: 120,
    search: {
      transform: (value) => ({ receiverAccount: value }),
    },
  },
  {
    title: '接收方',
    dataIndex: 'receiverUsername',
    key: 'receiverUsername',
    width: 120,
    render: (text) => (text !== null && text !== undefined ? text : '--'),
    search: {
      transform: (value) => ({ receiverUsername: value }),
    },
  },
  {
    title: '金额',
    dataIndex: 'amountStr',
    key: 'amountStr',
    width: 100,
    render: (text) => <Text strong>{text}</Text>,
    hideInSearch: true,
  },
  // {
  //   title: '金额范围',
  //   key: 'amountRange',
  //   hideInTable: true,
  //   valueType: 'digitRange',
  //   search: {
  //     transform: (value) => {
  //       if (!value) return {};
  //       const [min, max] = value;
  //       return {
  //         amountMin: min,
  //         amountMax: max,
  //       };
  //     },
  //   },
  //   fieldProps: {
  //     placeholder: ['最小金额', '最大金额'],
  //   },
  // },
  {
    title: '代币',
    dataIndex: 'tokenSymbol',
    key: 'tokenSymbol',
    width: 80,
    valueType: 'select',
    valueEnum:
      tokenSymbols.length > 0
        ? tokenSymbols.reduce((acc, symbol) => {
            acc[symbol] = { text: symbol };
            return acc;
          }, {} as Record<string, { text: string }>)
        : undefined,
    fieldProps: {
      loading: tokenLoading,
      placeholder: '选择代币',
      showSearch: true,
      allowClear: true,
      options: tokenSymbols.map((symbol) => ({ label: symbol, value: symbol })),
    },
  },

  {
    title: '发送交易ID',
    dataIndex: 'senderTxId',
    key: 'senderTxId',
    width: 100,
    render: (text) => (text && text !== 0 ? text : '--'),
    hideInSearch: true,
  },
  {
    title: '接收交易ID',
    dataIndex: 'receiverTxId',
    key: 'receiverTxId',
    width: 100,
    render: (text) => (text && text !== 0 ? text : '--'),
    hideInSearch: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    render: (text, record) => {
      // 直接使用 record.status，因为 text 参数可能是已渲染的 React 元素
      //console.log('Status render - using record.status:', record.status);
      return getStatusTag(record.status);
    },
    valueType: 'select',
    valueEnum: STATUS_OPTIONS.reduce((acc, option) => {
      acc[option.value] = { text: option.label };
      return acc;
    }, {} as Record<string, { text: string }>),
    fieldProps: {
      placeholder: '选择状态',
      allowClear: true,
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 140,
    // valueType: 'dateRange',
    //
    hideInSearch: true,

    // search: {
    //   transform: (value) => {
    //     if (!value) return {};
    //     return { createdAt: value };
    //   },
    // },
    defaultSortOrder: 'descend',
  },
  {
    title: '过期时间',
    dataIndex: 'expiresAt',
    key: 'expiresAt',
    width: 140,
    //valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    valueType: 'option',
    fixed: 'right',
    hideInSearch: true,
    render: (_, record) => [
      <Button
        key="detail"
        type="link"
        onClick={() => viewDetail(record.transferId!)}
      >
        详情
      </Button>,
    ],
  },
];

/**
 * 获取带有统一搜索字段的完整表格列
 */
export const getTransferColumns = (
  viewDetail: (transferId: number) => void,
  tokenSymbols: string[] = [],
  tokenLoading: boolean = false,
): ProColumns<ExtendedTransferItem>[] => {
  const baseColumns = getBaseTransferColumns(
    viewDetail,
    tokenSymbols,
    tokenLoading,
  );

  return useUnifiedFields(
    baseColumns,
    {
      dateRange: { search: true, display: false },
      firstAgent: { search: true, display: true },
      secondAgent: { search: true, display: true },
      thirdAgent: { search: true, display: true },
      telegramId: { search: true, display: true },
      telegramUsername: { search: true, display: true },
      firstName: { search: true, display: true },
    },
    undefined,
    0, // 在转账ID、发送方ID之后插入统一字段
  );
};
