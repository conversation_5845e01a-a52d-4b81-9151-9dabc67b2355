import React from 'react';
import { 
  Drawer, 
  Descriptions, 
  Card, 
  Typography, 
  Space, 
  Tag, 
  But<PERSON>,
  Divider,
  Alert,
  Spin
} from 'antd';
import { 
  CopyOutlined, 
  LinkOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import type { CallbackRecordDetail } from '../types';
import { 
  getCallbackStatusTag, 
  getCallbackTypeTag,
  getHttpStatusTag,
  getRetryCountTag,
  formatDateTime,
  canRetryCallback
} from '../utils';

const { Text, Paragraph } = Typography;

interface CallbackDetailDrawerProps {
  visible: boolean;
  loading: boolean;
  data: CallbackRecordDetail | null;
  onClose: () => void;
  onRetry?: (record: CallbackRecordDetail) => void;
}

/**
 * 回调记录详情抽屉组件
 */
const CallbackDetailDrawer: React.FC<CallbackDetailDrawerProps> = ({
  visible,
  loading,
  data,
  onClose,
  onRetry,
}) => {
  /**
   * 复制文本到剪贴板
   */
  const copyToClipboard = (text: string, successMessage: string = '复制成功') => {
    navigator.clipboard.writeText(text).then(() => {
      // 这里应该显示成功消息，但为了简化，暂时省略
      //console.log(successMessage);
    });
  };

  /**
   * 渲染状态指示器
   */
  const renderStatusIndicator = (status?: string) => {
    const icons = {
      pending: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      failed: <CloseCircleOutlined style={{ color: '#f5222d' }} />,
    };
    
    return icons[status as keyof typeof icons] || null;
  };

  /**
   * 渲染JSON数据
   */
  const renderJsonData = (data: string, title: string) => {
    if (!data) return null;
    
    try {
      const formatted = JSON.stringify(JSON.parse(data), null, 2);
      return (
        <Card size="small" title={title} style={{ marginBottom: 16 }}>
          <Paragraph
            copyable={{
              text: formatted,
              tooltips: ['复制', '已复制'],
            }}
          >
            <pre style={{ 
              background: '#f5f5f5', 
              padding: 12, 
              borderRadius: 4,
              fontSize: 12,
              maxHeight: 200,
              overflow: 'auto'
            }}>
              {formatted}
            </pre>
          </Paragraph>
        </Card>
      );
    } catch {
      return (
        <Card size="small" title={title} style={{ marginBottom: 16 }}>
          <Paragraph
            copyable={{
              text: data,
              tooltips: ['复制', '已复制'],
            }}
          >
            <pre style={{ 
              background: '#f5f5f5', 
              padding: 12, 
              borderRadius: 4,
              fontSize: 12,
              maxHeight: 200,
              overflow: 'auto'
            }}>
              {data}
            </pre>
          </Paragraph>
        </Card>
      );
    }
  };

  return (
    <Drawer
      title={
        <Space>
          <span>回调记录详情</span>
          {data?.id && <Tag color="blue">ID: {data.id}</Tag>}
        </Space>
      }
      width={800}
      open={visible}
      onClose={onClose}
      extra={
        data && canRetryCallback(data as any) && (
          <Button 
            type="primary" 
            onClick={() => onRetry?.(data)}
          >
            重试回调
          </Button>
        )
      }
    >
      <Spin spinning={loading}>
        {data && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="记录ID">
                  {data.id}
                </Descriptions.Item>
                <Descriptions.Item label="商户ID">
                  {data.merchantId}
                </Descriptions.Item>
                <Descriptions.Item label="回调类型">
                  {getCallbackTypeTag(data.callbackType)}
                </Descriptions.Item>
                <Descriptions.Item label="关联记录ID">
                  {data.relatedId || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="状态" span={2}>
                  <Space>
                    {renderStatusIndicator(data.status)}
                    {getCallbackStatusTag(data.status)}
                    {data.statusText && <Text type="secondary">({data.statusText})</Text>}
                  </Space>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 回调信息 */}
            <Card title="回调信息" style={{ marginBottom: 16 }}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="回调URL">
                  <Space>
                    <Text copyable={{ tooltips: ['复制URL', '已复制'] }}>
                      {data.callbackUrl}
                    </Text>
                    {data.callbackUrl && (
                      <Button
                        type="link"
                        size="small"
                        icon={<LinkOutlined />}
                        onClick={() => window.open(data.callbackUrl, '_blank')}
                      >
                        打开
                      </Button>
                    )}
                  </Space>
                </Descriptions.Item>
              </Descriptions>
              
              {/* 回调载荷 */}
              {data.payload && renderJsonData(data.payload, '回调载荷 (Payload)')}
            </Card>

            {/* 响应信息 */}
            <Card title="响应信息" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="响应状态码">
                  {getHttpStatusTag(data.responseCode)}
                </Descriptions.Item>
                <Descriptions.Item label="重试次数">
                  {getRetryCountTag(data.retryCount)}
                </Descriptions.Item>
              </Descriptions>
              
              {/* 响应内容 */}
              {data.responseBody && renderJsonData(data.responseBody, '响应内容 (Response Body)')}
            </Card>

            {/* 时间信息 */}
            <Card title="时间信息" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="创建时间">
                  {formatDateTime(data.createdAt)}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {formatDateTime(data.updatedAt)}
                </Descriptions.Item>
                <Descriptions.Item label="最后尝试时间" span={2}>
                  {formatDateTime(data.lastAttemptAt)}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 状态提示 */}
            {data.status === 'failed' && (
              <Alert
                message="回调失败"
                description="此回调已失败，您可以点击重试按钮重新发送回调请求。"
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            
            {data.status === 'pending' && (
              <Alert
                message="等待发送"
                description="此回调正在等待发送，系统将自动处理。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
          </div>
        )}
      </Spin>
    </Drawer>
  );
};

export default CallbackDetailDrawer;
