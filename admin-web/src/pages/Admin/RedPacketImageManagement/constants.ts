import { GetApiSystemRedPacketImagesStatus } from '@/api/model';

export const DEFAULT_PAGINATION = {
  pageSize: 20,
  showSizeChanger: false,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
};

export const RED_PACKET_IMAGE_STATUS = [
  {
    label: '待审核',
    value: GetApiSystemRedPacketImagesStatus.pending_review,
    color: 'processing',
  },
  {
    label: '审核通过',
    value: GetApiSystemRedPacketImagesStatus.success,
    color: 'success',
  },
  {
    label: '审核拒绝',
    value: GetApiSystemRedPacketImagesStatus.fail,
    color: 'error',
  },
];

/**
 * 表格滚动配置
 */
export const TABLE_SCROLL = {
  x: 1400,
};

export const REVIEW_ACTIONS = {
  APPROVE: 'success' as const,
  REJECT: 'fail' as const,
};
