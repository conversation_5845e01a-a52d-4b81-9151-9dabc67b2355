import { CheckCircleOutlined, EyeOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Image, Modal, Select, Space, Tag, Tooltip } from 'antd';
import React, { useMemo, useRef } from 'react';

import { getSystemRedPacket } from '@/api/interfaces/system-red-packet/system-red-packet';
import { GetApiSystemRedPacketImagesStatus } from '@/api/model';
import { useUnifiedFields } from '@/components/UnifiedFields';

import ReviewModal from './components/ReviewModal';
import { DEFAULT_PAGINATION, TABLE_SCROLL } from './constants';
import { useRedPacketImages, useTokenSymbols } from './hooks';
import type { RedPacketImageItem } from './types';
import { formatDate, formatRequestParams } from './utils';

const RedPacketImageManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const { tokenSymbols, tokenLoading } = useTokenSymbols();
  const {
    reviewModalVisible,
    currentImage,
    reviewLoading,
    imagePreviewVisible,
    previewImage,
    handleReview,
    setReviewModalVisible,
    setCurrentImage,
    setImagePreviewVisible,
  } = useRedPacketImages();

  /**
   * 获取红包封面列表
   */
  const fetchRedPacketImages = async (params: any) => {
    try {
      const apiParams = formatRequestParams(params);
      //console.log('API请求参数:', apiParams);
      const response = await getSystemRedPacket().getApiSystemRedPacketImages(
        apiParams,
      );
      //console.log('API完整响应:', response);
      //console.log('Response type:', typeof response);
      //console.log('Response keys:', Object.keys(response || {}));

      // 根据API类型定义，响应结构是直接的 { list, page }
      const list = response?.list || [];
      const total = response?.page?.totalSize || 0;

      //console.log('处理后的数据:', { list, total, listLength: list.length });
      //console.log('第一条数据:', list[0]);

      return {
        data: list,
        success: true,
        total: total,
      };
    } catch (error) {
      console.error('获取红包封面列表失败:', error);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  /**
   * 获取基础表格列（不包含统一搜索字段）
   */
  const getBaseColumns = useMemo(
    (): ProColumns<RedPacketImageItem>[] => [
      {
        title: '封面ID',
        dataIndex: 'redPacketImagesId',
        key: 'redPacketImagesId',
        width: 80,
        search: false,
      },
      {
        title: '封面图片',
        dataIndex: 'imagesUrl',
        key: 'imagesUrl',
        width: 120,
        search: false,
        render: (_, record) => (
          <Image
            src={record.imagesUrl}
            alt="红包封面"
            width={80}
            height={80}
            style={{ objectFit: 'cover', cursor: 'pointer' }}
            preview={{
              mask: <EyeOutlined />,
            }}
          />
        ),
      },
      // {
      //   title: '用户ID',
      //   dataIndex: 'userId',
      //   key: 'userId',
      //   width: 100,
      //   render: (text) => text || '--',
      // },
      // {
      //   title: '用户名',
      //   dataIndex: 'username',
      //   key: 'username',
      //   ellipsis: true,
      //   search: false,
      //   render: (text) => text || '--',
      // },
      // {
      //   title: '昵称',
      //   dataIndex: 'nickname',
      //   key: 'nickname',
      //   ellipsis: true,
      //   search: false,
      //   render: (text) => text || '--',
      // },
      // {
      //   title: '币种',
      //   dataIndex: 'symbol',
      //   key: 'symbol',
      //   width: 80,
      //   hideInTable: false,
      //   formItemProps: {
      //     label: '币种',
      //   },
      //   renderFormItem: () => (
      //     <Select
      //       placeholder="选择币种"
      //       loading={tokenLoading}
      //       allowClear
      //       showSearch
      //       optionFilterProp="children"
      //     >
      //       {tokenSymbols.map((symbol: string) => (
      //         <Select.Option key={symbol} value={symbol}>
      //           {symbol}
      //         </Select.Option>
      //       ))}
      //     </Select>
      //   ),
      //   render: () => {
      //     // 由于红包封面本身不包含币种信息，这里显示为占位符
      //     // 实际使用中可能需要根据业务逻辑调整
      //     return <Tag color="blue">--</Tag>;
      //   },
      // },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 120,
        valueType: 'select',
        valueEnum: {
          [GetApiSystemRedPacketImagesStatus.pending_review]: {
            text: '待审核',
            status: 'Processing',
          },
          [GetApiSystemRedPacketImagesStatus.success]: {
            text: '审核通过',
            status: 'Success',
          },
          [GetApiSystemRedPacketImagesStatus.fail]: {
            text: '审核拒绝',
            status: 'Error',
          },
        },
      },
      {
        title: '拒绝原因',
        dataIndex: 'refuseReason',
        key: 'refuseReason',
        width: 250,
        search: false,
        ellipsis: true,
        render: (_, record) => {
          if (record.status !== GetApiSystemRedPacketImagesStatus.fail) {
            return '--';
          }
          return (
            <div>
              {record.refuseReasonZh && (
                <div>
                  <Tag color="red">中文</Tag>
                  {record.refuseReasonZh.length > 20 ? (
                    <Tooltip title={record.refuseReasonZh}>
                      <span
                        style={{
                          display: 'inline-block',
                          maxWidth: '150px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          verticalAlign: 'bottom',
                        }}
                      >
                        {record.refuseReasonZh}
                      </span>
                    </Tooltip>
                  ) : (
                    <span>{record.refuseReasonZh}</span>
                  )}
                </div>
              )}
              {record.refuseReasonEn && (
                <div style={{ marginTop: 4 }}>
                  <Tag color="blue">英文</Tag>
                  {record.refuseReasonEn.length > 20 ? (
                    <Tooltip title={record.refuseReasonEn}>
                      <span
                        style={{
                          display: 'inline-block',
                          maxWidth: '150px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          verticalAlign: 'bottom',
                        }}
                      >
                        {record.refuseReasonEn}
                      </span>
                    </Tooltip>
                  ) : (
                    <span>{record.refuseReasonEn}</span>
                  )}
                </div>
              )}
            </div>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        search: false,
        render: (text) => formatDate(text as string),
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 160,
        search: false,
        render: (text) => formatDate(text as string),
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right',
        search: false,
        render: (_, record) => {
          if (
            record.status !== GetApiSystemRedPacketImagesStatus.pending_review
          ) {
            return <span style={{ color: '#999' }}>已审核</span>;
          }
          return (
            <Space>
              <Button
                type="link"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => {
                  setCurrentImage(record);
                  setReviewModalVisible(true);
                }}
              >
                审核
              </Button>
            </Space>
          );
        },
      },
    ],
    [setCurrentImage, setReviewModalVisible, tokenSymbols, tokenLoading],
  );

  /**
   * 使用统一字段系统获取完整表格列
   */
  const columns = useMemo(() => {
    return useUnifiedFields(
      getBaseColumns,
      {
        dateRange: { search: true, display: false },
        firstAgent: { search: true, display: true },
        secondAgent: { search: true, display: true },
        thirdAgent: { search: true, display: true },
        telegramId: { search: true, display: true },
        telegramUsername: { search: true, display: true },
        firstName: { search: true, display: true },
      },
      undefined,
      2, // 在封面ID、封面图片之后插入统一字段
    );
  }, [getBaseColumns]);

  return (
    <PageContainer title="红包封面管理">
      <ProTable<RedPacketImageItem>
        headerTitle="红包封面列表"
        actionRef={actionRef}
        rowKey="redPacketImagesId"
        search={{
          labelWidth: 120,
        }}
        request={fetchRedPacketImages}
        columns={columns}
        pagination={DEFAULT_PAGINATION}
        scroll={TABLE_SCROLL}
      />

      <ReviewModal
        visible={reviewModalVisible}
        onCancel={() => {
          setReviewModalVisible(false);
          setCurrentImage(undefined);
        }}
        onSubmit={(formData) => handleReview(formData, actionRef)}
        loading={reviewLoading}
        image={currentImage}
      />

      <Modal
        open={imagePreviewVisible}
        footer={null}
        onCancel={() => setImagePreviewVisible(false)}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </PageContainer>
  );
};

export default RedPacketImageManagement;
