import { getSystemCommon } from '@/api/interfaces/system-common/system-common';
import { getSystemRedPacket } from '@/api/interfaces/system-red-packet/system-red-packet';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useState } from 'react';
import type { RedPacketImageInfo, ReviewFormData } from './types';

/**
 * 获取代币符号列表的自定义Hook
 */
export const useTokenSymbols = () => {
  const { data, loading } = useRequest(
    async () => {
      try {
        const response = await getSystemCommon().getApiSystemTokensSymbols();
        return [...new Set((response.symbols || []).map((item) => item))];
      } catch (error) {
        console.error('获取代币符号列表失败:', error);
        return [];
      }
    },
    {
      cacheKey: 'token-symbols',
    },
  );

  return {
    tokenSymbols: data || [],
    tokenLoading: loading,
  };
};

export const useRedPacketImages = () => {
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [currentImage, setCurrentImage] = useState<
    RedPacketImageInfo | undefined
  >();
  const [reviewLoading, setReviewLoading] = useState(false);
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  const handleReview = async (formData: ReviewFormData, actionRef?: any) => {
    if (!currentImage?.redPacketImagesId) return;

    setReviewLoading(true);
    try {
      const api = getSystemRedPacket();
      await api.postApiSystemRedPacketImagesRedPacketImagesIdReview(
        currentImage.redPacketImagesId,
        formData,
      );
      message.success('审核操作成功');
      setReviewModalVisible(false);
      actionRef?.current?.reload();
    } catch (error) {
      console.error('审核拒绝:', error);
      message.error('审核操作失败，请重试');
    } finally {
      setReviewLoading(false);
    }
  };

  return {
    reviewModalVisible,
    currentImage,
    reviewLoading,
    imagePreviewVisible,
    previewImage,
    handleReview,
    setReviewModalVisible,
    setCurrentImage,
    setImagePreviewVisible,
    setPreviewImage,
  };
};
