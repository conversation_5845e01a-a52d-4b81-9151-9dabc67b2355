import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Space, message, Spin, Card, Row, Col, Switch, InputNumber } from 'antd';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { getWithdrawalConfig } from '@/api/interfaces/withdrawal-config/withdrawal-config';
import type { AdminApiApiSystemV1WithdrawalRiskControlSettingInfo } from '@/api/model';

const { Option } = Select;

/**
 * 提现风控设置表单组件
 */
const WithdrawalRiskControlSettingsForm: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<AdminApiApiSystemV1WithdrawalRiskControlSettingInfo[]>([]);
  const [formKey, setFormKey] = useState(0);

  // 获取设置列表
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const api = getWithdrawalConfig();
      const response = await api.getApiSystemWithdrawalRiskControlSettings({
        page: 1,
        pageSize: 100,
      });
      
      //console.log('Risk Control API Response:', response);
      
      // 处理响应数据 - 注意这里的结构
      const responseData = response as any;
      
      // 数据直接在 response.data 中
      const data = responseData?.data || [];
      //console.log('Risk Control Data:', data);
      
      // Sort data by id to maintain consistent ordering
      const sortedData = [...data].sort((a: any, b: any) => (a.id || 0) - (b.id || 0));
      setSettings(sortedData);
      
      // 设置表单初始值
      if (sortedData.length > 0) {
        const formData = {
          settings: sortedData.map((item: AdminApiApiSystemV1WithdrawalRiskControlSettingInfo) => ({
            id: item.id,
            controlType: item.controlType,
            timePeriod: item.timePeriod,
            timeUnit: item.timeUnit,
            maxTimes: item.maxTimes,
            maxAmount: item.maxAmount,
            status: item.status === 1,
          })),
        };
        //console.log('Risk Control Form Data:', formData);
        form.setFieldsValue(formData);
        // 强制更新表单
        setFormKey(prev => prev + 1);
      } else {
        // 如果没有数据，设置一个默认的空行
        form.setFieldsValue({
          settings: [{ 
            controlType: 'times_limit', 
            timePeriod: 1, 
            timeUnit: 'day', 
            status: true 
          }],
        });
        setFormKey(prev => prev + 1);
      }
    } catch (error) {
      message.error('获取风控设置失败');
      console.error('Risk Control Error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);
      
      const api = getWithdrawalConfig();
      const settingsData = values.settings || [];
      
      // 分离已存在和新建的配置
      const existingSettings = settingsData.filter((s: any) => s.id);
      const newSettings = settingsData.filter((s: any) => !s.id);
      
      // 更新已存在的配置
      for (const setting of existingSettings) {
        await api.putApiSystemWithdrawalRiskControlSettingsId(setting.id, {
          controlType: setting.controlType,
          timePeriod: setting.timePeriod,
          timeUnit: setting.timeUnit,
          maxTimes: setting.controlType === 'times_limit' ? setting.maxTimes : undefined,
          maxAmount: setting.controlType === 'amount_limit' ? setting.maxAmount : undefined,
          status: setting.status ? 1 : 0,
        });
      }
      
      // 创建新配置
      for (const setting of newSettings) {
        await api.postApiSystemWithdrawalRiskControlSettings({
          controlType: setting.controlType,
          timePeriod: setting.timePeriod,
          timeUnit: setting.timeUnit,
          maxTimes: setting.controlType === 'times_limit' ? setting.maxTimes : undefined,
          maxAmount: setting.controlType === 'amount_limit' ? setting.maxAmount : undefined,
          status: setting.status ? 1 : 0,
        });
      }
      
      // 删除已移除的配置
      const currentIds = settingsData.filter((s: any) => s.id).map((s: any) => s.id);
      const toDelete = settings.filter((s: AdminApiApiSystemV1WithdrawalRiskControlSettingInfo) => s.id && !currentIds.includes(s.id));
      
      if (toDelete.length > 0) {
        await api.deleteApiSystemWithdrawalRiskControlSettings({
          ids: toDelete.map(s => s.id!),
        });
      }
      
      message.success('保存成功');
      fetchSettings(); // 重新加载数据
    } catch (error) {
      message.error('保存失败');
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  // 取消操作
  const handleCancel = () => {
    form.resetFields();
    fetchSettings(); // 重新加载数据
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card style={{ minHeight: 400 }}>
      <Form
        key={formKey}
        form={form}
        layout="vertical"
        style={{ maxWidth: 1200 }}
      >
        <Form.List name="settings">
          {(fields, { add, remove }) => {
            //console.log('Risk Control Form.List fields:', fields);
            //console.log('Risk Control Form values:', form.getFieldsValue());
            return (
            <>
              {fields.map((field) => {
                const controlType = form.getFieldValue(['settings', field.name, 'controlType']);
                
                return (
                  <Card
                    key={field.key}
                    style={{ marginBottom: 16, backgroundColor: '#fafafa' }}
                  >
                    <Row gutter={[16, 16]} align="middle">
                      <Col xs={24} sm={12} md={6} lg={5}>
                        <Form.Item
                          {...field}
                          label="风控类型"
                          name={[field.name, 'controlType']}
                          rules={[{ required: true, message: '请选择风控类型' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Select placeholder="请选择" style={{ width: '100%' }}>
                            <Option value="times_limit">提现次数风控</Option>
                            <Option value="amount_limit">提现金额风控</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      
                      <Col xs={24} sm={12} md={6} lg={4}>
                        <Form.Item
                          {...field}
                          label="时间周期"
                          name={[field.name, 'timePeriod']}
                          rules={[{ required: true, message: '请输入时间周期' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <InputNumber
                            min={1}
                            placeholder="时间周期"
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      
                      <Col xs={24} sm={12} md={6} lg={4}>
                        <Form.Item
                          {...field}
                          label="时间单位"
                          name={[field.name, 'timeUnit']}
                          rules={[{ required: true, message: '请选择时间单位' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Select placeholder="请选择" style={{ width: '100%' }}>
                            <Option value="hour">小时</Option>
                            <Option value="day">天</Option>
                            <Option value="week">周</Option>
                            <Option value="month">月</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      
                      {controlType === 'times_limit' && (
                        <Col xs={24} sm={12} md={6} lg={5}>
                          <Form.Item
                            {...field}
                            label="最大提现次数"
                            name={[field.name, 'maxTimes']}
                            rules={[{ required: true, message: '请输入最大次数' }]}
                            style={{ marginBottom: 0 }}
                          >
                            <InputNumber
                              min={1}
                              placeholder="最大次数"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      )}
                      
                      {controlType === 'amount_limit' && (
                        <Col xs={24} sm={12} md={6} lg={5}>
                          <Form.Item
                            {...field}
                            label="最大提现金额"
                            name={[field.name, 'maxAmount']}
                            rules={[{ required: true, message: '请输入最大金额' }]}
                            style={{ marginBottom: 0 }}
                          >
                            <Input
                              placeholder="最大金额"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      )}
                      
                      <Col xs={24} sm={12} md={6} lg={3}>
                        <Form.Item
                          {...field}
                          label="状态"
                          name={[field.name, 'status']}
                          valuePropName="checked"
                          style={{ marginBottom: 0 }}
                        >
                          <Switch
                            checkedChildren="启用"
                            unCheckedChildren="禁用"
                          />
                        </Form.Item>
                      </Col>
                      
                      <Col xs={24} sm={12} md={6} lg={3}>
                        <Form.Item label=" " colon={false} style={{ marginBottom: 0 }}>
                          <Button
                            type="text"
                            danger
                            icon={<MinusCircleOutlined />}
                            onClick={() => remove(field.name)}
                            style={{ width: '100%' }}
                          >
                            删除
                          </Button>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                );
              })}
              
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add({ 
                    controlType: 'times_limit', 
                    timePeriod: 1, 
                    timeUnit: 'day', 
                    status: true 
                  })}
                  icon={<PlusCircleOutlined />}
                  style={{ width: '200px' }}
                >
                  添加配置
                </Button>
              </Form.Item>
            </>
            );
          }}
        </Form.List>
        
        <Form.Item style={{ marginTop: 24 }}>
          <Space>
            <Button type="primary" onClick={handleSave} loading={saving}>
              保存设置
            </Button>
            <Button onClick={handleCancel}>
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default WithdrawalRiskControlSettingsForm;