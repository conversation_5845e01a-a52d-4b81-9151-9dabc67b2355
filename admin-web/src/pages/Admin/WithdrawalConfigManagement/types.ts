// 提现金额设置
export interface WithdrawalAmountSetting {
  id?: number;
  currency: string;
  network: string;
  minAmount: string;
  maxAmount: string;
  status: number;
  createdAt?: string;
  updatedAt?: string;
}

// 提现审核设置
export interface WithdrawalApprovalSetting {
  id?: number;
  currency: string;
  network: string;
  autoReleaseMin: string;
  autoReleaseMax: string;
  approvalAutoMin: string;
  approvalAutoMax: string;
  approvalManualMin: string;
  approvalManualMax: string;
  status: number;
  createdAt?: string;
  updatedAt?: string;
}

// 提现手续费设置
export interface WithdrawalFeeSetting {
  id?: number;
  currency: string;
  network: string;
  amountMin: string;
  amountMax: string;
  feeType: 'fixed' | 'percent';
  feeValue: string;
  status: number;
  createdAt?: string;
  updatedAt?: string;
}

// 表单值类型
export interface WithdrawalAmountFormValues {
  currency: string;
  network: string;
  minAmount: string;
  maxAmount: string;
  status: number;
}

export interface WithdrawalApprovalFormValues {
  currency: string;
  network: string;
  autoReleaseMin: string;
  autoReleaseMax: string;
  approvalAutoMin: string;
  approvalAutoMax: string;
  approvalManualMin: string;
  approvalManualMax: string;
  status: number;
}

export interface WithdrawalFeeFormValues {
  currency: string;
  network: string;
  amountMin: string;
  amountMax: string;
  feeType: 'fixed' | 'percent';
  feeValue: string;
  status: number;
}

// 币种和网络选项
export interface CurrencyOption {
  label: string;
  value: string;
}

export interface NetworkOption {
  label: string;
  value: string;
}

// 手续费类型选项
export const FEE_TYPE_OPTIONS = [
  { label: '固定金额', value: 'fixed' },
  { label: '百分比', value: 'percent' },
];

// 状态选项
export const STATUS_OPTIONS = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 常用币种选项
export const CURRENCY_OPTIONS: CurrencyOption[] = [
  { label: 'USDT', value: 'USDT' },
  { label: 'CNY', value: 'CNY' },
  { label: 'ETH', value: 'ETH' },
  { label: 'TRX', value: 'TRX' },
];

// 常用网络选项
export const NETWORK_OPTIONS: NetworkOption[] = [
  { label: 'TRC20', value: 'TRC20' },
  { label: 'ERC20', value: 'ERC20' },
  { label: 'TRON', value: 'TRON' },
  { label: 'ETH', value: 'ETH' },
  { label: 'Fiat', value: 'Fiat' },
];
