import { Form, Input, Modal, Space } from 'antd';
import React from 'react';
import { FundWithdrawalRecord } from '../types';

const { TextArea } = Input;

interface ReviewModalProps {
  visible: boolean;
  loading: boolean;
  record: FundWithdrawalRecord | null;
  type: 'approve' | 'reject';
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

/**
 * 提现审核模态框
 */
const ReviewModal: React.FC<ReviewModalProps> = ({
  visible,
  loading,
  record,
  type,
  onSubmit,
  onCancel,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开或关闭时重置表单
  React.useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={type === 'approve' ? '确认批准' : '审核拒绝'}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      maskClosable={false}
      destroyOnClose
      width={400}
    >
      <Form form={form} layout="vertical">
        {type === 'approve' ? (
          <Form.Item name="adminRemark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息(可选)" />
          </Form.Item>
        ) : (
          <>
            <Form.Item
              name="refuseReason"
              label="拒绝原因(非必填)"
              rules={[{ required: false, message: '请输入拒绝原因' }]}
            >
              <TextArea rows={3} placeholder="请输入拒绝原因(可选)" />
            </Form.Item>
           
          </>
        )}
      </Form>
    </Modal>
  );
};

export default ReviewModal;
