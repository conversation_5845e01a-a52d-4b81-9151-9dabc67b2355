import { LinkOutlined } from '@ant-design/icons';
import {
  Button,
  Descriptions,
  Divider,
  Drawer,
  Popconfirm,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React from 'react';
import {
  formatAmount,
  formatDateTime,
} from '../constants';
import { WithdrawalDetailRecord } from '../types';
import {
  BLOCKCHAIN_EXPLORERS,
  getBlockchainExplorerAddressUrl,
  getBlockchainExplorerTxUrl,
} from '../utils';

const { Title, Text, Link } = Typography;

interface WithdrawalDetailDrawerProps {
  visible: boolean;
  loading: boolean;
  detail: WithdrawalDetailRecord | null;
  onClose: () => void;
}

/**
 * 提现详情抽屉组件
 */
const WithdrawalDetailDrawer: React.FC<WithdrawalDetailDrawerProps> = ({
  visible,
  loading,
  detail,
  onClose,
}) => {
  // 获取审核状态标签
  const getAuditStatusTag = (auditStatus?: number) => {
    if (!auditStatus) return '-';
    const auditStatusMap: Record<number, { text: string; color: string }> = {
      1: { text: '免审', color: 'blue' },
      2: { text: '待审核', color: 'orange' },
      3: { text: '审核通过', color: 'green' },
      4: { text: '审核拒绝', color: 'red' },
    };
    const status = auditStatusMap[auditStatus];
    return status ? (
      <Tag color={status.color}>{status.text}</Tag>
    ) : (
      '-'
    );
  };

  // 获取处理状态标签
  const getProcessingStatusTag = (processingStatus?: number) => {
    if (processingStatus === undefined) return '-';
    const processingStatusMap: Record<number, { text: string; color: string }> = {
      0: { text: '无', color: 'default' },
      1: { text: '自动放币处理中', color: 'processing' },
      2: { text: '待冷钱包', color: 'warning' },
      3: { text: '待人工转账', color: 'orange' },
      4: { text: '成功', color: 'success' },
      5: { text: '失败', color: 'error' },
    };
    const status = processingStatusMap[processingStatus];
    return status ? (
      <Tag color={status.color}>{status.text}</Tag>
    ) : (
      '-'
    );
  };

  // 渲染地址到区块链浏览器的链接
  const renderAddressLink = (address?: string, chain?: string) => {
    if (!address || !chain) return <Text copyable>{address || '-'}</Text>;

    const url = getBlockchainExplorerAddressUrl(address, chain);
    const explorerName =
      BLOCKCHAIN_EXPLORERS[
        chain.toUpperCase() as keyof typeof BLOCKCHAIN_EXPLORERS
      ]?.name || '区块浏览器';

    return (
      <Space>
        <Text copyable>{address}</Text>
        {url && (
          <Popconfirm
            title={`确定要跳转到${explorerName}查看地址详情吗？`}
            onConfirm={() => window.open(url, '_blank', 'noopener,noreferrer')}
            okText="确定"
            cancelText="取消"
          >
            <Link>
              <LinkOutlined />
            </Link>
          </Popconfirm>
        )}
      </Space>
    );
  };

  // 渲染交易哈希到区块链浏览器的链接
  const renderTxExplorerLink = (txHash?: string, chain?: string) => {
    if (!txHash || !chain) return <Text copyable>{txHash || '-'}</Text>;

    const url = getBlockchainExplorerTxUrl(txHash, chain);
    const explorerName =
      BLOCKCHAIN_EXPLORERS[
        chain.toUpperCase() as keyof typeof BLOCKCHAIN_EXPLORERS
      ]?.name || '区块浏览器';

    const displayText =
      txHash.length > 20
        ? `${txHash.substring(0, 10)}...${txHash.substring(txHash.length - 10)}`
        : txHash;

    return (
      <Space>
        <Text copyable>{displayText}</Text>
        {url && (
          <Popconfirm
            title={`确定要跳转到${explorerName}查看交易详情吗？`}
            onConfirm={() => window.open(url, '_blank', 'noopener,noreferrer')}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" icon={<LinkOutlined />} style={{ padding: 0 }}>
              在{explorerName}上查看
            </Button>
          </Popconfirm>
        )}
      </Space>
    );
  };

  return (
    <Drawer
      title="提现详情"
      width={700} // Can adjust width if needed, RechargeDetailDrawer uses 640
      placement="right"
      onClose={onClose}
      open={visible}
      styles={{
        body: {
          paddingBottom: '80px',
        },
      }}
    >
      <Spin spinning={loading}>
        {detail ? (
          <>
            <Title level={4}>基本信息</Title>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="提现ID" span={2}>
                {detail.userWithdrawsId}
              </Descriptions.Item>
              <Descriptions.Item label="订单号" span={2}>
                {detail.orderNo}
              </Descriptions.Item>
              <Descriptions.Item label="用户ID">
                {detail.userId}
              </Descriptions.Item>
              <Descriptions.Item label="用户账号">
                {detail.account}
              </Descriptions.Item>
              <Descriptions.Item label="用户昵称">
                {detail.firstName}
              </Descriptions.Item>
              <Descriptions.Item label="审核状态">
                {getAuditStatusTag(detail.auditStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="处理状态">
                {getProcessingStatusTag(detail.processingStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {formatDateTime(detail.createdAt)}
              </Descriptions.Item>
              {detail.checkedAt && (
                <Descriptions.Item label="审核时间" span={2}>
                  {formatDateTime(detail.checkedAt)}
                </Descriptions.Item>
              )}
              {detail.processingAt && (
                <Descriptions.Item label="处理时间" span={2}>
                  {formatDateTime(detail.processingAt)}
                </Descriptions.Item>
              )}
              {detail.completedAt && (
                <Descriptions.Item label="完成时间" span={2}>
                  {formatDateTime(detail.completedAt)}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Divider />

            <Title level={4}>金额信息</Title>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="币种">
                {detail.symbol}
              </Descriptions.Item>
              <Descriptions.Item label="链">{detail.chain}</Descriptions.Item>
              <Descriptions.Item label="提现金额">
                {formatAmount(detail.amount, detail.symbol)}
              </Descriptions.Item>
              <Descriptions.Item label="手续费">
                {formatAmount(detail.handlingFee, detail.symbol)}
              </Descriptions.Item>
              <Descriptions.Item label="实际到账" span={2}>
                {formatAmount(detail.actualAmount, detail.symbol)}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={4}>地址信息</Title>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="钱包ID">
                {detail.walletId || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="提现地址">
                {renderAddressLink(detail.address, detail.chain)}
              </Descriptions.Item>
              {detail.recipientName && (
                <Descriptions.Item label="收款人姓名">
                  {detail.recipientName}
                </Descriptions.Item>
              )}
              {detail.recipientAccount && (
                <Descriptions.Item label="收款账户">
                  {detail.recipientAccount}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="交易哈希">
                {renderTxExplorerLink(detail.txHash, detail.chain)}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={4}>备注信息</Title>
            <Descriptions bordered column={1}>
              {/* <Descriptions.Item label="用户备注">
                {detail.userRemark || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="管理员备注">
                {detail.adminRemark || '-'}
              </Descriptions.Item> */}
              {detail.auditStatus === 4 && (
                <>
                  <Descriptions.Item label="拒绝原因">
                    {detail.refuseReasonZh || '-'}
                  </Descriptions.Item>
                  {/* <Descriptions.Item label="拒绝原因(英文)">
                    {detail.refuseReasonEn || '-'}
                  </Descriptions.Item> */}
                </>
              )}
              {detail.processingStatus === 5 && (
                <Descriptions.Item label="错误信息">
                  {detail.errorMessage || '-'}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Divider />

            <Title level={4}>通知信息</Title>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="通知状态">
                {(() => {
                  // 默认值为 0 (未发送)
                  const notificationSent = detail.notificationSent ?? 0;
                  return notificationSent === 1 ? (
                    <Tag color="green">已发送</Tag>
                  ) : (
                    <Tag color="default">未发送</Tag>
                  );
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="通知时间">
                {formatDateTime(detail.notificationSentAt)}
              </Descriptions.Item>
            </Descriptions>
          </>
        ) : (
          <div>未找到提现记录详情</div>
        )}
      </Spin>
    </Drawer>
  );
};

export default WithdrawalDetailDrawer;
