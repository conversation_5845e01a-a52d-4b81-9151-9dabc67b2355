import { Form, Input, Modal, Radio, Space } from 'antd';
import React from 'react';
import { FundWithdrawalRecord } from '../types';

const { TextArea } = Input;

interface UpdateStatusModalProps {
  visible: boolean;
  loading: boolean;
  record: FundWithdrawalRecord | null;
  newStatus: 4 | 5; // 4-成功, 5-失败
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

/**
 * 更新提现状态模态框
 */
const UpdateStatusModal: React.FC<UpdateStatusModalProps> = ({
  visible,
  loading,
  record,
  newStatus,
  onSubmit,
  onCancel,
}) => {
  const [form] = Form.useForm();

  // 当模态框关闭时重置表单
  React.useEffect(() => {
    if (!visible) {
      form.resetFields();
    } else {
      form.setFieldsValue({
        processingStatus: newStatus,
      });
    }
  }, [visible, form, newStatus]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 4:
        return '成功';
      case 5:
        return '失败';
      default:
        return '未知状态';
    }
  };

  return (
    <Modal
      title={`更新提现状态为: ${getStatusText(newStatus)}`}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      maskClosable={false}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          processingStatus: newStatus,
        }}
      >
        <Form.Item label="提现记录信息">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              用户: {record?.account} ({record?.nickname})
            </div>
            <div>
              金额: {record?.amount} {record?.symbol}
            </div>
            <div>地址: {record?.address}</div>
          </Space>
        </Form.Item>

        <Form.Item name="processingStatus" label="新状态">
          <Radio.Group disabled>
            <Radio value={4}>成功</Radio>
            <Radio value={5}>失败</Radio>
          </Radio.Group>
        </Form.Item>

        {newStatus === 4 && (
          <Form.Item
            name="txHash"
            label="交易哈希"
            rules={[{ required: true, message: '请输入交易哈希' }]}
          >
            <Input placeholder="请输入区块链交易哈希" />
          </Form.Item>
        )}

        {newStatus === 5 && (
          <Form.Item
            name="errorMessage"
            label="错误信息"
            rules={[{ required: true, message: '请输入错误信息' }]}
          >
            <TextArea rows={2} placeholder="请输入失败原因" />
          </Form.Item>
        )}

        <Form.Item name="adminRemark" label="管理员备注">
          <TextArea rows={3} placeholder="请输入管理员备注(可选)" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateStatusModal;
