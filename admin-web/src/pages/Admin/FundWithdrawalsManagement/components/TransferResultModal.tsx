import { Form, Input, Modal, Radio, Space } from 'antd';
import React from 'react';
import { FundWithdrawalRecord } from '../types';

const { TextArea } = Input;

interface TransferResultModalProps {
  visible: boolean;
  loading: boolean;
  record: FundWithdrawalRecord | null;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

/**
 * 转账结果模态框
 */
const TransferResultModal: React.FC<TransferResultModalProps> = ({
  visible,
  loading,
  record,
  onSubmit,
  onCancel,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开或关闭时重置表单
  React.useEffect(() => {
    if (visible) {
      form.resetFields();
      form.setFieldsValue({
        result: 'success',
      });
    }
  }, [visible, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="更新转账结果"
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      maskClosable={false}
      destroyOnClose
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          result: 'success',
        }}
      >
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <div>订单号: {record?.orderNo}</div>
          <div>
            提现金额: {record?.amount} {record?.symbol}
          </div>
          <div>提现地址: {record?.address}</div>
        </Space>

        <Form.Item
          name="result"
          label="转账结果"
          rules={[{ required: true, message: '请选择转账结果' }]}
        >
          <Radio.Group>
            <Radio value="success">成功</Radio>
            <Radio value="failed">失败</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.result !== currentValues.result
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('result') === 'success' ? (
              <Form.Item
                name="txHash"
                label="交易哈希"
                rules={[{ required: true, message: '请输入交易哈希' }]}
              >
                <Input placeholder="请输入交易哈希" />
              </Form.Item>
            ) : (
              <Form.Item
                name="errorMessage"
                label="失败原因"
                rules={[{ required: true, message: '请输入失败原因' }]}
              >
                <TextArea rows={3} placeholder="请输入失败原因" />
              </Form.Item>
            )
          }
        </Form.Item>

        {/* <Form.Item name="adminRemark" label="备注">
          <TextArea rows={2} placeholder="请输入备注信息(可选)" />
        </Form.Item> */}
      </Form>
    </Modal>
  );
};

export default TransferResultModal;