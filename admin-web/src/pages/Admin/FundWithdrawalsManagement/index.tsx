import { PageContainer, ProTable } from '@ant-design/pro-components';
import React from 'react';
import {
  AddressDetailModal,
  ReviewModal,
  TransferResultModal,
  UpdateStatusModal,
  WithdrawalDetailDrawer,
  WithdrawalReviewModal,
} from './components';
import { getColumns } from './constants';
import { useFundWithdrawalsManagement } from './hooks';

/**
 * Fund Withdrawals Management Page
 * Provides UI for managing fund withdrawal records
 */
const FundWithdrawalsManagement: React.FC = () => {
  const {
    actionRef,
    loading,
    detailVisible,
    detailLoading,
    currentRecord,
    withdrawalDetail,
    reviewModalVisible,
    reviewType,
    updateStatusModalVisible,
    newStatus,
    addressModalVisible,
    addressRecord,
    transferResultModalVisible,
    withdrawalReviewModalVisible,
    tokenSymbols,
    tokenLoading,
    fetchFundWithdrawals,
    handleView,
    handleDetailClose,
    handleApprove,
    // handleReject,
    handleReviewSubmit,
    handleReviewCancel,
    handleUpdateStatus,
    handleUpdateStatusSubmit,
    handleUpdateStatusCancel,
    handleAddressClick,
    handleAddressModalClose,
    handleUpdateTransferResult,
    handleTransferResultSubmit,
    handleTransferResultCancel,
    handleWithdrawalReviewApprove,
    handleWithdrawalReviewReject,
    handleWithdrawalReviewCancel,
  } = useFundWithdrawalsManagement();

  // 获取表格列配置
  const columns = getColumns(
    tokenSymbols,
    tokenLoading,
    handleView,
    handleApprove,
    // handleReject,
    handleAddressClick,
    handleUpdateTransferResult,
  );

  return (
    <PageContainer title="提现管理">
      <ProTable
        actionRef={actionRef}
        rowKey="userWithdrawsId"
        loading={loading}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        options={{
          density: true,
          fullScreen: true,
          reload: true,
          setting: true,
        }}
        request={fetchFundWithdrawals}
        columns={columns}
        scroll={{ x: 2000 }} // 设置水平滚动，当列宽超过2000px时出现滚动条
        pagination={{
          showSizeChanger: false,
          showQuickJumper: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
      />

      {/* 提现详情抽屉 */}
      <WithdrawalDetailDrawer
        visible={detailVisible}
        loading={detailLoading}
        detail={withdrawalDetail}
        onClose={handleDetailClose}
      />

      {/* 审核模态框 */}
      <ReviewModal
        visible={reviewModalVisible}
        loading={loading}
        record={currentRecord}
        type={reviewType}
        onSubmit={handleReviewSubmit}
        onCancel={handleReviewCancel}
      />

      {/* 更新状态模态框 */}
      <UpdateStatusModal
        visible={updateStatusModalVisible}
        loading={loading}
        record={currentRecord}
        newStatus={newStatus}
        onSubmit={handleUpdateStatusSubmit}
        onCancel={handleUpdateStatusCancel}
      />

      {/* 地址详情模态框 */}
      <AddressDetailModal
        visible={addressModalVisible}
        record={addressRecord}
        onClose={handleAddressModalClose}
      />

      {/* 转账结果模态框 */}
      <TransferResultModal
        visible={transferResultModalVisible}
        loading={loading}
        record={currentRecord}
        onSubmit={handleTransferResultSubmit}
        onCancel={handleTransferResultCancel}
      />

      {/* 提现审核模态框 */}
      <WithdrawalReviewModal
        visible={withdrawalReviewModalVisible}
        loading={loading}
        record={currentRecord}
        onApprove={handleWithdrawalReviewApprove}
        onReject={handleWithdrawalReviewReject}
        onCancel={handleWithdrawalReviewCancel}
      />
    </PageContainer>
  );
};

export default FundWithdrawalsManagement;
