import { getSystemCommon } from '@/api/interfaces/system-common/system-common';
import { getSystemUserWithdraw } from '@/api/interfaces/system-user-withdraw/system-user-withdraw';
import type { ActionType } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useRef, useState } from 'react';
import {
  FetchWithdrawalsParams,
  FetchWithdrawalsResponse,
  FundWithdrawalRecord,
  ReviewWithdrawalParams,
  UpdateStatusParams,
  WithdrawalDetailRecord,
} from './types';

/**
 * 格式化请求参数
 * @param params ProTable参数
 * @returns 格式化后的API参数
 */
const formatRequestParams = (params: any): FetchWithdrawalsParams => {
  const { current, dateRange, ...rest } = params;

  return {
    page: current,
    // 直接传递dateRange字符串，格式为 "YYYY-MM-DD,YYYY-MM-DD"
    dateRange: dateRange || undefined,
    ...rest,
  };
};

/**
 * 自定义Hook用于获取代币符号列表
 */
export const useTokenSymbols = () => {
  const { data, loading } = useRequest(
    async () => {
      try {
        const response = await getSystemCommon().getApiSystemTokensSymbols();
        if (response.symbols && response.tokenIds) {
          return response.symbols.map((symbolStr, index) => {
            const symbol = symbolStr;
            return {
              symbol,
              tokenId: response.tokenIds?.[index] || 0,
            };
          });
        }
        return [];
      } catch (error) {
        console.error('获取代币符号列表失败:', error);
        return [];
      }
    },
    {
      cacheKey: 'token-symbols',
    },
  );

  return {
    tokenSymbols: data || [],
    tokenLoading: loading,
  };
};

/**
 * Custom hook for managing Fund Withdrawals page logic.
 */
export const useFundWithdrawalsManagement = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [currentRecord, setCurrentRecord] =
    useState<FundWithdrawalRecord | null>(null);
  const [withdrawalDetail, setWithdrawalDetail] =
    useState<WithdrawalDetailRecord | null>(null);

  // 审核相关状态
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [reviewType, setReviewType] = useState<'approve' | 'reject'>('approve');

  // 更新状态相关状态
  const [updateStatusModalVisible, setUpdateStatusModalVisible] =
    useState(false);
  const [newStatus, setNewStatus] = useState<4 | 5>(4); // 4-成功, 5-失败

  // 地址详情模态框相关状态
  const [addressModalVisible, setAddressModalVisible] = useState(false);
  const [addressRecord, setAddressRecord] =
    useState<FundWithdrawalRecord | null>(null);

  // 转账结果模态框相关状态
  const [transferResultModalVisible, setTransferResultModalVisible] = useState(false);

  // 提现审核模态框相关状态
  const [withdrawalReviewModalVisible, setWithdrawalReviewModalVisible] = useState(false);

  // 获取代币符号列表
  const { tokenSymbols, tokenLoading } = useTokenSymbols();

  /**
   * 获取提现记录列表
   * @param params 查询参数
   */
  const fetchFundWithdrawals = async (
    params: FetchWithdrawalsParams & {
      pageSize?: number;
      current?: number;
    },
  ): Promise<FetchWithdrawalsResponse> => {
    setLoading(true);
    try {
      const formattedParams = formatRequestParams(params);
      const response = await getSystemUserWithdraw().getApiSystemUserWithdraws(
        formattedParams,
      );

      const responseData = (response.data || []).map((item) => ({
        ...item,
        userWithdrawsId: item.userWithdrawsId as number, // 假设 userWithdrawsId 总是存在
      })) as FundWithdrawalRecord[];

      return {
        data: responseData,
        total: (response.page as any)?.total || 0, // 绕过 AdminApiApiCommonPageResponse 可能不正确的类型定义
        success: true,
      };
    } catch (error) {
      console.error('获取提现记录失败:', error);
      message.error('获取提现记录失败');
      return {
        data: [],
        total: 0,
        success: false,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取提现记录详情
   * @param id 提现记录ID
   */
  const fetchWithdrawalDetail = async (id: number) => {
    setDetailLoading(true);
    try {
      const response =
        await getSystemUserWithdraw().getApiSystemUserWithdrawsWithdrawId(id);
      if (response.data) {
        setWithdrawalDetail({
          ...response.data,
          userWithdrawsId: response.data.userWithdrawsId as number, // 假设 userWithdrawsId 总是存在
        } as WithdrawalDetailRecord);
      } else {
        setWithdrawalDetail(null);
      }
    } catch (error) {
      console.error('获取提现详情失败:', error);
      message.error('获取提现详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  /**
   * 审核提现记录
   * @param withdrawId 提现记录ID
   * @param params 审核参数
   */
  const reviewWithdrawal = async (
    withdrawId: number,
    params: ReviewWithdrawalParams,
  ) => {
    setLoading(true);
    try {
      await getSystemUserWithdraw().putApiSystemUserWithdrawsWithdrawIdReview(
        withdrawId,
        params,
      );
      message.success('审核操作成功');
      actionRef.current?.reload();
      return true;
    } catch (error) {
      console.error('审核提现记录失败:', error);
      message.error('审核提现记录失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 更新提现记录状态
   * @param withdrawId 提现记录ID
   * @param params 状态更新参数
   */
  const updateWithdrawalStatus = async (
    withdrawId: number,
    params: UpdateStatusParams,
  ) => {
    setLoading(true);
    try {
      await getSystemUserWithdraw().putApiSystemUserWithdrawsWithdrawIdStatus(
        withdrawId,
        {
          auditStatus: params.auditStatus,
          processingStatus: params.processingStatus,
          txHash: params.txHash,
          errorMessage: params.errorMessage,
          adminRemark: params.adminRemark,
        } as any, // 临时使用any，直到API类型更新
      );
      message.success('状态更新成功');
      actionRef.current?.reload();
      return true;
    } catch (error) {
      console.error('更新提现状态失败:', error);
      message.error('更新提现状态失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 查看详情
   */
  const handleView = (record: FundWithdrawalRecord) => {
    setCurrentRecord(record);
    setDetailVisible(true);
    fetchWithdrawalDetail(record.userWithdrawsId);
  };

  /**
   * 关闭详情抽屉
   */
  const handleDetailClose = () => {
    setDetailVisible(false);
    setWithdrawalDetail(null);
  };

  /**
   * 批准提现（显示审核弹窗）
   */
  const handleApprove = (record: FundWithdrawalRecord) => {
    setCurrentRecord(record);
    setWithdrawalReviewModalVisible(true);
  };

  /**
   * 拒绝提现
   */
  // const handleReject = (record: FundWithdrawalRecord) => {
  //   setCurrentRecord(record);
  //   setReviewType('reject');
  //   setReviewModalVisible(true);
  // };

  /**
   * 提交审核
   */
  const handleReviewSubmit = async (values: any) => {
    if (!currentRecord) return;

    setLoading(true);
    try {
      const api = getSystemUserWithdraw();
      
      if (reviewType === 'approve') {
        await api.postApiSystemUserWithdrawsWithdrawIdApprove(
          currentRecord.userWithdrawsId,
          {
            adminRemark: values.adminRemark,
          }
        );
        message.success('批准成功');
      } else {
        await api.postApiSystemUserWithdrawsWithdrawIdReject(
          currentRecord.userWithdrawsId,
          {
            refuseReason: values.refuseReason,
            adminRemark: values.adminRemark,
          }
        );
        message.success('拒绝成功');
      }
      
      setReviewModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      console.error('审核操作失败:', error);
      message.error('审核操作失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 取消审核
   */
  const handleReviewCancel = () => {
    setReviewModalVisible(false);
  };

  /**
   * 更新状态
   */
  const handleUpdateStatus = (
    record: FundWithdrawalRecord,
    status: 4 | 5, // 4-成功, 5-失败
  ) => {
    setCurrentRecord(record);
    setNewStatus(status);
    setUpdateStatusModalVisible(true);
  };

  /**
   * 提交状态更新
   */
  const handleUpdateStatusSubmit = async (values: any) => {
    if (!currentRecord) return;

    const params: UpdateStatusParams = {
      auditStatus: 3, // 审核通过
      processingStatus: newStatus, // 4-成功, 5-失败
      ...values,
    };

    const success = await updateWithdrawalStatus(
      currentRecord.userWithdrawsId,
      params,
    );
    if (success) {
      setUpdateStatusModalVisible(false);
    }
  };

  /**
   * 取消状态更新
   */
  const handleUpdateStatusCancel = () => {
    setUpdateStatusModalVisible(false);
  };

  /**
   * 点击地址查看详情
   */
  const handleAddressClick = (record: FundWithdrawalRecord) => {
    setAddressRecord(record);
    setAddressModalVisible(true);
  };

  /**
   * 关闭地址详情模态框
   */
  const handleAddressModalClose = () => {
    setAddressModalVisible(false);
    setAddressRecord(null);
  };

  /**
   * 处理转账结果更新
   */
  const handleUpdateTransferResult = (record: FundWithdrawalRecord) => {
    setCurrentRecord(record);
    setTransferResultModalVisible(true);
  };

  /**
   * 提交转账结果
   */
  const handleTransferResultSubmit = async (values: any) => {
    if (!currentRecord) return;

    setLoading(true);
    try {
      const api = getSystemUserWithdraw();
      await api.putApiSystemUserWithdrawsWithdrawIdTransferResult(
        currentRecord.userWithdrawsId,
        {
          result: values.result,
          txHash: values.txHash,
          errorMessage: values.errorMessage,
          adminRemark: values.adminRemark,
        }
      );
      message.success('转账结果更新成功');
      setTransferResultModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      console.error('更新转账结果失败:', error);
      message.error('更新转账结果失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 取消转账结果更新
   */
  const handleTransferResultCancel = () => {
    setTransferResultModalVisible(false);
  };

  /**
   * 处理审核弹窗的同意操作
   */
  const handleWithdrawalReviewApprove = async () => {
    if (!currentRecord) return;

    setLoading(true);
    try {
      const api = getSystemUserWithdraw();
      await api.postApiSystemUserWithdrawsWithdrawIdApprove(
        currentRecord.userWithdrawsId,
        {
          adminRemark: '通过审核',
        }
      );
      message.success('审核通过');
      setWithdrawalReviewModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      console.error('审核拒绝:', error);
      message.error('审核拒绝');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理审核弹窗的拒绝操作
   */
  const handleWithdrawalReviewReject = () => {
    setWithdrawalReviewModalVisible(false);
    // 显示拒绝原因弹窗
    setReviewType('reject');
    setReviewModalVisible(true);
  };

  /**
   * 取消审核弹窗
   */
  const handleWithdrawalReviewCancel = () => {
    setWithdrawalReviewModalVisible(false);
  };

  return {
    actionRef,
    loading,
    detailVisible,
    detailLoading,
    currentRecord,
    withdrawalDetail,
    reviewModalVisible,
    reviewType,
    updateStatusModalVisible,
    newStatus,
    addressModalVisible,
    addressRecord,
    transferResultModalVisible,
    withdrawalReviewModalVisible,
    tokenSymbols,
    tokenLoading,
    fetchFundWithdrawals,
    handleView,
    handleDetailClose,
    handleApprove,
    // handleReject,
    handleReviewSubmit,
    handleReviewCancel,
    handleUpdateStatus,
    handleUpdateStatusSubmit,
    handleUpdateStatusCancel,
    handleAddressClick,
    handleAddressModalClose,
    handleUpdateTransferResult,
    handleTransferResultSubmit,
    handleTransferResultCancel,
    handleWithdrawalReviewApprove,
    handleWithdrawalReviewReject,
    handleWithdrawalReviewCancel,
  };
};
