

/**
 * Represents the type of fiat withdrawal
 */
export enum FiatWithdrawalType {
  AlipayAccount = 'alipay_account', // 支付宝账号
  AlipayQR = 'alipay_qr', // 支付宝二维码
  WechatQR = 'wechat_qr', // 微信二维码
}

/**
 * Interface for a single fund withdrawal record based on API response.
 */
export interface FundWithdrawalRecord {
  userWithdrawsId: number; // 提现记录ID
  userId: number; // 用户ID
  account: string; // 用户账号
  nickname: string; // 用户昵称
  tokenId: number; // 币种ID
  symbol: string; // 币种符号
  chain: string; // 链名称
  walletId: string; // 钱包ID
  orderNo: string; // 订单号
  address: string; // 提币地址
  amount: number; // 申请提现金额
  handlingFee: number; // 手续费
  actualAmount: number; // 实际到账金额
  auditStatus: number; // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
  processingStatus: number; // 处理状态: 0-无, 1-自动放币处理中, 2-处理中(待冷钱包), 3-待人工转账, 4-成功, 5-失败
  txHash?: string; // 交易哈希
  userRemark?: string; // 用户备注
  adminRemark?: string; // 管理员备注
  createdAt: string; // 创建时间
  checkedAt?: string; // 审核时间
  processingAt?: string; // 处理时间
  completedAt?: string; // 完成时间
  notificationSent?: number; // 是否已发送通知: 0-未发送, 1-已发送
  notificationSentAt?: string; // 通知发送时间
  fiatType?: string; // 法币提现类型
  recipientName?: string; // 法币收款人姓名
  recipientAccount?: string; // 法币收款账户
  recipientQrcode?: string; // 法币收款二维码
  telegramId?: string; // Telegram ID
  telegramUsername?: string; // Telegram用户名
  firstName?: string; // 名字
  showApprovalButton?: boolean; // 是否显示审核按钮
  showTransferButton?: boolean; // 是否显示转账结果按钮
}

/**
 * Interface for withdrawal detail record
 */
export interface WithdrawalDetailRecord extends FundWithdrawalRecord {
  refuseReasonZh?: string; // 拒绝原因(中文)
  refuseReasonEn?: string; // 拒绝原因(英文)
  errorMessage?: string; // 错误信息
}

/**
 * Interface for the parameters used to fetch withdrawal records.
 * Matches the API parameters
 */
export interface FetchWithdrawalsParams {
  page?: number; // 页码
  pageSize?: number; // 每页数量
  userId?: number; // 用户ID
  account?: string; // 用户账号
  username?: string; // 用户名
  tokenId?: number; // 币种ID
  symbol?: string; // 币种符号
  chain?: string; // 链名称
  address?: string; // 提币地址
  orderNo?: string; // 订单号
  auditStatus?: number; // 审核状态: 0-全部, 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
  processingStatus?: number; // 处理状态: 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败
  dateRange?: string; // 创建时间范围，格式：2025-01-01,2025-01-31
  amountMin?: number; // 最小金额
  amountMax?: number; // 最大金额
}

/**
 * Interface for the response structure when fetching withdrawal records.
 */
export interface FetchWithdrawalsResponse {
  data: FundWithdrawalRecord[];
  total: number;
  success: boolean;
}

/**
 * Interface for review action request
 */
export interface ReviewWithdrawalParams {
  action: 'approve' | 'reject'; // 操作类型: approve-通过, reject-拒绝
  refuseReasonZh?: string; // 拒绝原因(中文)
  refuseReasonEn?: string; // 拒绝原因(英文)
  adminRemark?: string; // 管理员备注
}

/**
 * Interface for update status request
 */
export interface UpdateStatusParams {
  auditStatus: 3; // 审核状态: 3-审核通过
  processingStatus: 4 | 5; // 处理状态: 4-成功, 5-失败
  txHash?: string; // 交易哈希
  errorMessage?: string; // 错误信息
  adminRemark?: string; // 管理员备注
}

/**
 * Interface for token symbol item
 */
export interface TokenSymbolItem {
  symbol: string;
  tokenId: number;
}
