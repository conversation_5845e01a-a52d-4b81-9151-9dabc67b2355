import { useUnifiedFields } from '@/components/UnifiedFields';
import type {
  ProColumns,
} from '@ant-design/pro-components';
import { DatePicker, Select, Tag } from 'antd';
import dayjs from 'dayjs';

import {
  FiatWithdrawalType,
  FundWithdrawalRecord,
} from './types';

const { RangePicker } = DatePicker;

/**
 * Mapping from FiatWithdrawalType enum to display text.
 */
export const FIAT_TYPE_MAP: Record<string, string> = {
  [FiatWithdrawalType.AlipayAccount]: '支付宝账号',
  [FiatWithdrawalType.AlipayQR]: '支付宝二维码',
  [FiatWithdrawalType.WechatQR]: '微信二维码',
};



/**
 * 格式化日期时间
 */
export const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return '-';
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化金额
 */
export const formatAmount = (amount?: number, symbol?: string) => {
  if (amount === undefined || amount === null) return '-';
  return `${amount.toFixed(8)} ${symbol || ''}`;
};

/**
 * Columns definition for the Fund Withdrawals ProTable.
 */
export const getColumns = (
  tokenSymbols: { symbol: string; tokenId: number }[],
  tokenLoading: boolean,
  handleView: (record: FundWithdrawalRecord) => void,
  handleApprove: (record: FundWithdrawalRecord) => void,
  // handleReject: (record: FundWithdrawalRecord) => void,
  handleAddressClick: (record: FundWithdrawalRecord) => void,
  handleUpdateTransferResult?: (record: FundWithdrawalRecord) => void,
): ProColumns<FundWithdrawalRecord>[] => {
  const baseColumns: ProColumns<FundWithdrawalRecord>[] = [
    {
      title: '记录ID',
      dataIndex: 'userWithdrawsId',
      key: 'userWithdrawsId',
      ellipsis: true,
      copyable: true,
      search: false,
      width: 120,
      fixed: 'left', // 固定在左侧
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      ellipsis: true,
      copyable: true,
      width: 150,
      fixed: 'left', // 固定在左侧
    },
    // {
    //   title: '用户ID',
    //   dataIndex: 'userId',
    //   key: 'userId',
    //   ellipsis: true,
    //   copyable: true,
    // },
    // {
    //   title: '用户账号',
    //   dataIndex: 'account',
    //   key: 'account',
    //   ellipsis: true,
    // },
    // {
    //   title: '用户昵称',
    //   dataIndex: 'nickname',
    //   key: 'nickname',
    //   ellipsis: true,
    // },
    {
      title: '提现金额',
      dataIndex: 'amount',
      key: 'amount',
      valueType: 'money',
      align: 'right',
      search: false,
      width: 120,
      fixed: 'left', // 固定在左侧
      render: (_, record) => formatAmount(record.amount),
    },
    {
      title: '币种',
      dataIndex: 'symbol',
      key: 'symbol',
      align: 'center',
      width: 80,
      fixed: 'left', // 固定在左侧
      renderFormItem: () => (
        <Select
          placeholder="选择币种"
          loading={tokenLoading}
          allowClear
          showSearch
          optionFilterProp="children"
        >
          {(tokenSymbols || []).map((item) => {
            return (
              <Select.Option key={item.tokenId} value={item.symbol}>
                {item.symbol}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      title: '链',
      dataIndex: 'chain',
      key: 'chain',
      align: 'center',
      width: 100,
    },
    {
      title: '手续费',
      dataIndex: 'handlingFee',
      key: 'handlingFee',
      valueType: 'money',
      align: 'right',
      search: false,
      width: 120,
      render: (_, record) => formatAmount(record.handlingFee, record.symbol),
    },
    {
      title: '实际到账',
      dataIndex: 'actualAmount',
      key: 'actualAmount',
      valueType: 'money',
      align: 'right',
      search: false,
      width: 120,
      render: (_, record) => formatAmount(record.actualAmount, record.symbol),
    },
    {
      title: '提现地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
      copyable: true,
      width: 150,
      render: (text, record) => {
        // 判断是否为法币提现
        if (record.fiatType) {
          const typeName = FIAT_TYPE_MAP[record.fiatType] || '法币提现';
          return <a onClick={() => handleAddressClick(record)}>{typeName}</a>;
        }
        // 加密货币提现
        if (!text) return '-';
        return <a onClick={() => handleAddressClick(record)}>{text}</a>;
      },
    },

    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      align: 'center',
      width: 100,
      valueEnum: {
        0: { text: '全部', status: 'Default' },
        1: { text: '免审', status: 'Processing' },
        2: { text: '待审核', status: 'Warning' },
        3: { text: '审核通过', status: 'Success' },
        4: { text: '审核拒绝', status: 'Error' },
      },
      render: (_, record) => {
        const auditStatusMap: Record<number, { text: string; color: string }> = {
          1: { text: '免审', color: 'blue' },
          2: { text: '待审核', color: 'orange' },
          3: { text: '审核通过', color: 'green' },
          4: { text: '审核拒绝', color: 'red' },
        };
        const status = auditStatusMap[record.auditStatus];
        return status ? (
          <Tag color={status.color}>{status.text}</Tag>
        ) : (
          '-'
        );
      },
    },
    {
      title: '处理状态',
      dataIndex: 'processingStatus',
      key: 'processingStatus',
      align: 'center',
      width: 120,
      valueEnum: {
        0: { text: '无', status: 'Default' },
        1: { text: '自动放币处理中', status: 'Processing' },
        2: { text: '待冷钱包', status: 'Warning' },
        3: { text: '待人工转账', status: 'Warning' },
        4: { text: '成功', status: 'Success' },
        5: { text: '失败', status: 'Error' },
      },
      render: (_, record) => {
        const processingStatusMap: Record<number, { text: string; color: string }> = {
          0: { text: '无', color: 'default' },
          1: { text: '自动放币处理中', color: 'processing' },
          2: { text: '待冷钱包', color: 'warning' },
          3: { text: '待人工转账', color: 'orange' },
          4: { text: '成功', color: 'success' },
          5: { text: '失败', color: 'error' },
        };
        const status = processingStatusMap[record.processingStatus];
        return status ? (
          <Tag color={status.color}>{status.text}</Tag>
        ) : (
          '-'
        );
      },
    },
    {
      title: '通知状态',
      dataIndex: 'notificationSent',
      key: 'notificationSent',
      align: 'center',
      search: false,
      width: 100,
      render: (_, record) => {
        // 默认值为 0 (未发送)
        const notificationSent = record.notificationSent ?? 0;
        return notificationSent === 1 ? (
          <Tag color="green">已发送</Tag>
        ) : (
          <Tag color="default">未发送</Tag>
        );
      },
    },
    {
      title: '通知时间',
      dataIndex: 'notificationSentAt',
      key: 'notificationSentAt',
      //valueType: 'dateTime',
      search: false,
      width: 160,
      render: (_, record) => formatDateTime(record.notificationSentAt),
    },
    // {
    //   title: '交易哈希',
    //   dataIndex: 'txHash',
    //   key: 'txHash',
    //   ellipsis: true,
    //   copyable: true,
    //   search: false,
    //   render: (text) => (text ? text : '-'),
    // },
    {
      title: '申请时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      //valueType: 'dateTime',
      search: false,
      width: 160,
      render: (_, record) => formatDateTime(record.createdAt),
    },
    {
      title: '时间范围',
      key: 'dateRange',
      hideInTable: true,
      dataIndex: 'dateRange',
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          if (!value || !Array.isArray(value) || value.length !== 2) return {};
          const [start, end] = value;
          if (!start || !end) return {};
          // Ensure we have dayjs objects and handle both dayjs and moment formats
          const startDate = start.format ? start.format('YYYY-MM-DD') : start;
          const endDate = end.format ? end.format('YYYY-MM-DD') : end;
          return {
            dateRange: `${startDate},${endDate}`,
          };
        },
      },
      renderFormItem: () => (
        <RangePicker
          format="YYYY-MM-DD"
          placeholder={['开始日期', '结束日期']}
        />
      ),
    },
    // {
    //   title: '金额范围',
    //   key: 'amountRange',
    //   hideInTable: true,
    //   search: {
    //     transform: (value: [number, number]) => {
    //       return {
    //         amountMin: value[0],
    //         amountMax: value[1],
    //       };
    //     },
    //   },
    //   renderFormItem: () => (
    //     <Space>
    //       <Select
    //         placeholder="最小金额"
    //         style={{ width: 100 }}
    //         options={[
    //           { label: '无限制', value: '' },
    //           { label: '10', value: 10 },
    //           { label: '100', value: 100 },
    //           { label: '1000', value: 1000 },
    //         ]}
    //       />
    //       <span>-</span>
    //       <Select
    //         placeholder="最大金额"
    //         style={{ width: 100 }}
    //         options={[
    //           { label: '无限制', value: '' },
    //           { label: '100', value: 100 },
    //           { label: '1000', value: 1000 },
    //           { label: '10000', value: 10000 },
    //         ]}
    //       />
    //     </Space>
    //   ),
    // },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      width: 200,
      fixed: 'right', // 固定在右侧
      render: (_, record) => {
        const actions = [
          <a key="view" onClick={() => handleView(record)}>
            查看详情
          </a>,
        ];
        
        // 根据后端返回的 showApprovalButton 字段显示审核按钮
        if (record.showApprovalButton) {
          actions.push(
            <a
              key="approve"
              style={{ color: 'blue' }}
              onClick={() => handleApprove(record)}
            >
              审核
            </a>
            // <a
            //   key="reject"
            //   style={{ color: 'red' }}
            //   onClick={() => handleReject(record)}
            // >
            //   拒绝
            // </a>
          );
        }
        
        // 根据 showTransferButton 显示转账结果按钮
        if (record.showTransferButton) {
          actions.push(
            <a
              key="transfer"
              style={{ color: 'blue' }}
              onClick={() => handleUpdateTransferResult?.(record)}
            >
              转账结果
            </a>
          );
        }
        
        return actions;
      },
    },
  ];

  // 使用 useUnifiedFields 添加额外的搜索和展示字段
  return useUnifiedFields(
    baseColumns as any,
    {
      firstAgent: { search: true, display: true }, // 一级代理
      secondAgent: { search: true, display: true }, // 二级代理
      thirdAgent: { search: true, display: true }, // 三级代理
      telegramId: { search: true, display: true }, // Telegram ID
      telegramUsername: { search: true, display: true }, // Telegram 用户名
      firstName: { search: true, display: true }, // First Name
    },
    undefined,
    0,
  ) as any; // 在用户昵称之后插入统一字段
};
