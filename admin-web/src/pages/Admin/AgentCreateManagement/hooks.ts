// Hooks for Agent Create Management
import { Form, message } from 'antd';
import { useState } from 'react';
import type { AgentFormData } from './types';

/**
 * 模拟创建代理的函数
 * @param data 代理表单数据
 * @returns Promise<boolean> 表示操作是否成功
 */
const createMockAgent = async (data: AgentFormData): Promise<boolean> => {
  //console.log('Submitting mock agent data:', data);
  // 模拟 API 调用延迟
  await new Promise((resolve) => {
    setTimeout(resolve, 1000);
  });

  // 模拟成功或失败
  const success = Math.random() > 0.3; // 70% 成功率
  if (success) {
    message.success('代理创建成功 (模拟)');
    return true;
  } else {
    message.error('代理创建失败 (模拟)');
    return false;
  }
};

export const useAgentCreateForm = () => {
  const [form] = Form.useForm<AgentFormData>();
  const [loading, setLoading] = useState(false);

  const handleFinish = async (values: AgentFormData) => {
    setLoading(true);
    try {
      const success = await createMockAgent(values);
      if (success) {
        form.resetFields(); // 成功后清空表单
      }
      return success;
    } catch (error) {
      console.error('Error submitting agent form:', error);
      message.error('提交出错，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    form,
    loading,
    handleFinish,
  };
};
