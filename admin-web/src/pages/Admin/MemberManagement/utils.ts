import moment from 'moment';
import { MEMBER_STATUS } from './constants';
import { MemberStatus } from './types';

/**
 * 获取会员状态信息
 * @param status - 会员状态值
 * @returns 状态信息，包含标签和颜色
 */
export const getMemberStatusInfo = (status?: MemberStatus) => {
  const statusInfo = MEMBER_STATUS.find((item) => item.value === status);
  return {
    label: statusInfo?.label || '未知',
    value: status,
    color: statusInfo?.color || 'default',
  };
};

/**
 * 格式化API请求参数用于会员列表
 * @param params - 输入搜索参数
 * @returns 格式化后的参数
 */
export const formatRequestParams = (params: Record<string, any>) => {
  const { current, pageSize, ...restParams } = params;

  // 移除空参数
  const filteredParams = Object.entries(restParams || {}).reduce(
    (result, [key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        result[key] = value;
      }
      return result;
    },
    {} as Record<string, any>,
  );

  return {
    page: current || 1,
    pageSize: pageSize || 10,
    ...filteredParams,
  };
};

/**
 * 将日期格式化为本地字符串
 * @param dateString - 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateString?: string) => {
  //console.log(dateString);
  if (!dateString) return '-';

  return moment(dateString).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 检查文件是否为有效图片
 * @param file - 要检查的文件
 * @param maxSize - 最大文件大小（字节）
 * @returns 文件是否有效
 */
export const isValidImage = (file: File, maxSize: number) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size <= maxSize;

  if (!isImage) {
    console.error('文件必须是图片格式!');
    return false;
  }

  if (!isLt5M) {
    console.error(`图片大小必须小于${maxSize / (1024 * 1024)}MB!`);
    return false;
  }

  return true;
};
