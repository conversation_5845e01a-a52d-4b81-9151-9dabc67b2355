import { getSystemRedPacket } from '@/api/interfaces/system-red-packet/system-red-packet';
import type {
  GetApiSystemRedPacketsParams,
  XpayapiApiSystemV1RedPacketAdminInfoType,
  XpayapiApiSystemV1RedPacketClaimAdminInfoType,
} from '@/api/model';
import type { ActionType } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message, Modal } from 'antd';
import { useCallback, useRef, useState } from 'react';
import type { RedPacketSearchParams } from './types';
import { formatRequestParams, formatResponseData } from './utils';

/**
 * 红包管理功能的自定义钩子
 */
export const useRedPacketManagement = () => {
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [currentRedPacket, setCurrentRedPacket] =
    useState<XpayapiApiSystemV1RedPacketAdminInfoType | null>(null);
  const [claimRecords, setClaimRecords] = useState<
    XpayapiApiSystemV1RedPacketClaimAdminInfoType[]
  >([]);

  /**
   * 处理获取红包列表请求
   */
  const handleRequest = useCallback(async (params: RedPacketSearchParams) => {
    try {
      const formattedParams = formatRequestParams(
        params,
      ) as GetApiSystemRedPacketsParams;
      const response = await getSystemRedPacket().getApiSystemRedPackets(
        formattedParams,
      );
      //console.log(response);
      return formatResponseData(response);
    } catch (error) {
      console.error('获取红包列表失败:', error);
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  }, []);

  /**
   * 获取红包详情
   */
  const fetchRedPacketDetail = useCallback(async (redPacketId: number) => {
    try {
      setDetailLoading(true);
      const response =
        await getSystemRedPacket().getApiSystemRedPacketsRedPacketId(
          redPacketId,
        );
      setCurrentRedPacket(response.redPacket || null);
      setClaimRecords(response.claims || []);
      setDetailVisible(true);
    } catch (error) {
      console.error('获取红包详情失败:', error);
    } finally {
      setDetailLoading(false);
    }
  }, []);

  /**
   * 处理取消红包
   */
  const handleCancel = useCallback(async (redPacketId: number) => {
    Modal.confirm({
      title: '确认取消',
      content: '您确定要取消这个红包吗？此操作无法撤销。',
      onOk: async () => {
        try {
          setCancelLoading(true);
          await getSystemRedPacket().postApiSystemRedPacketsRedPacketIdCancel(
            redPacketId,
            {},
          );
          message.success('红包已成功取消');
          actionRef.current?.reload();
          setDetailVisible(false);
        } catch (error) {
          console.error('取消红包失败:', error);
        } finally {
          setCancelLoading(false);
        }
      },
    });
  }, []);

  /**
   * 关闭详情抽屉
   */
  const closeDetail = useCallback(() => {
    setDetailVisible(false);
  }, []);

  /**
   * 重新加载红包列表
   */
  const { loading: reloadLoading, run: reloadList } = useRequest(
    () => {
      actionRef.current?.reload();
      return Promise.resolve();
    },
    {
      manual: true,
    },
  );

  return {
    actionRef,
    detailVisible,
    detailLoading,
    cancelLoading,
    currentRedPacket,
    claimRecords,
    loading: reloadLoading,
    handleRequest,
    fetchRedPacketDetail,
    handleCancel,
    closeDetail,
    reloadList,
  };
};
