import { getSystemCommon } from '@/api/interfaces/system-common/system-common';
import { getSystemWallet } from '@/api/interfaces/system-wallet/system-wallet';
import { AdminApiApiSystemV1AdjustBalanceReqType } from '@/api/model/adminApiApiSystemV1AdjustBalanceReqType';
import type { ActionType } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useRef, useState } from 'react';
import type {
  AdjustBalanceParams,
  FetchFundAssetsParams,
  FetchFundAssetsResponse,
  FundAsset,
} from './types';

/**
 * Interface for the return value of useFundAssetsManagement hook
 */
export interface UseFundAssetsManagementResult {
  actionRef: React.MutableRefObject<ActionType | undefined>;
  loading: boolean;
  tokenSymbols: string[];
  tokenSymbolsLoading: boolean;
  fetchFundAssets: (
    params: FetchFundAssetsParams,
  ) => Promise<FetchFundAssetsResponse>;
  adjustModalVisible: boolean;
  setAdjustModalVisible: (visible: boolean) => void;
  selectedAsset: FundAsset | null;
  showAdjustModal: (asset: FundAsset) => void;
  handleAdjustSubmit: (values: {
    amount: string;
    reason: string;
    type: AdminApiApiSystemV1AdjustBalanceReqType;
  }) => Promise<void>;
  adjustLoading: boolean;
}

/**
 * Custom hook for managing Fund Assets Management page logic.
 */
export const useFundAssetsManagement = (): UseFundAssetsManagementResult => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<FundAsset | null>(null);
  const [adjustLoading, setAdjustLoading] = useState(false);

  // Fetch token symbols for dropdown
  const { data: tokenSymbolsData, loading: tokenSymbolsLoading } = useRequest(
    async () => {
      try {
        const response = await getSystemCommon().getApiSystemTokensSymbols();
        if (response.symbols) {
          // Symbols are returned as plain strings
          return response.symbols.filter(Boolean);
        }
        return [];
      } catch (error) {
        console.error('Failed to fetch token symbols:', error);
        return [];
      }
    },
    {
      cacheKey: 'token-symbols',
    },
  );

  /**
   * Fetches fund assets data from the API.
   */
  const fetchFundAssets = async (
    params: FetchFundAssetsParams,
  ): Promise<FetchFundAssetsResponse> => {
    setLoading(true);
    //console.log('Fetching fund assets with params:', params);

    try {
      // Convert ProTable params to API params
      const apiParams = {
        page: params.current || 1,
        pageSize: params.pageSize || 10,
        account: params.account,
        username: params.username,
        symbol: params.symbol,
        tokenId: params.tokenId,
        // 添加统一字段的搜索条件
        dateRange: params.dateRange,
        firstAgentName: params.firstAgentName,
        secondAgentName: params.secondAgentName,
        thirdAgentName: params.thirdAgentName,
        telegramId: params.telegramId,
        telegramUsername: params.telegramUsername,
        firstName: params.firstName,
      };

      const response = await getSystemWallet().getApiSystemWallets(apiParams);

      setLoading(false);

      return {
        data: response.data || [],
        total: response.page?.totalSize || 0,
        success: true,
      };
    } catch (error) {
      console.error('Error fetching fund assets:', error);
      setLoading(false);
      message.error('获取资金资产列表失败');

      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  /**
   * Opens the adjust balance modal for a specific asset.
   */
  const showAdjustModal = (asset: FundAsset) => {
    setSelectedAsset(asset);
    setAdjustModalVisible(true);
  };

  /**
   * Handles the adjustment of an asset balance.
   */
  const handleAdjustSubmit = async (values: {
    amount: string;
    reason: string;
    type: AdminApiApiSystemV1AdjustBalanceReqType;
  }) => {
    if (!selectedAsset) {
      return;
    }

    try {
      setAdjustLoading(true);

      const params: AdjustBalanceParams = {
        walletId: selectedAsset.walletId!,
        amount: Math.abs(parseFloat(values.amount)), // Always use positive amount
        type: values.type, // Use the selected operation type
        changeReason: values.reason,
      };

      // Call the API to adjust the balance
      const response =
        await getSystemWallet().postApiSystemWalletsAdjustBalance(params);

      if (response.success) {
        message.success(`调整余额成功`);
        setAdjustModalVisible(false);
        // Refresh the table data
        actionRef.current?.reload();
      } else {
        message.error('调整余额失败');
      }
    } catch (error) {
      console.error('Error adjusting balance:', error);
      message.error('调整余额失败');
    } finally {
      setAdjustLoading(false);
    }
  };

  return {
    actionRef,
    loading,
    tokenSymbols: tokenSymbolsData || [],
    tokenSymbolsLoading,
    fetchFundAssets,
    adjustModalVisible,
    setAdjustModalVisible,
    selectedAsset,
    showAdjustModal,
    handleAdjustSubmit,
    adjustLoading,
  };
};
