import {
  /* PlusOutlined, */ EditOutlined /* DeleteOutlined */,
} from '@ant-design/icons'; // Removed unused imports
import {
  // ProTable, // Removed unused import
  ActionType,
  PageContainer,
} from '@ant-design/pro-components';
import {
  Button,
  Form,
  /* Input, */ message,
  Modal,
  /* Popconfirm, */ Spin,
  Tabs,
} from 'antd'; // Removed unused imports
import React, { useEffect, useRef, useState } from 'react';
import { useConfigManagement } from './hooks';
import {
  CategoryFormData,
  ConfigCategory,
  ConfigItem,
  ConfigItemFormData,
  ValueTypes,
} from './types';
// import ValueInputRenderer from './components/ValueInputRenderer'; // Moved to ConfigItemModal
import CategoryModal from './components/CategoryModal';
import ConfigItemModal from './components/ConfigItemModal';
import ConfigItemTable from './components/ConfigItemTable';

const { TabPane } = Tabs;
// const { Option } = Select; // Moved to ConfigItemModal

// parseValue function moved to ConfigItemTable.tsx or utils

const ConfigManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const {
    categories,
    // configItems, // No longer needed directly here, fetched by ProTable
    activeCategoryId,
    setActiveCategoryId,
    loadingCategories, // Use loadingCategories for the main spin/tabs loading state
    // loadingItems, // No longer needed, ProTable handles its loading
    actionLoading,
    createCategory,
    updateCategory,
    removeCategory,
    createConfigItem,
    editConfigItem,
    removeConfigItem,
    loadConfigItems, // Add loadConfigItems for ProTable request
  } = useConfigManagement();

  const [isItemModalVisible, setIsItemModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<ConfigItem | null>(null);
  const [itemForm] = Form.useForm<ConfigItemFormData>();

  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ConfigCategory | null>(
    null,
  );
  const [categoryForm] = Form.useForm<CategoryFormData>();

  // Reset item form when modal closes
  useEffect(() => {
    if (!isItemModalVisible) {
      itemForm.resetFields();
      setEditingRecord(null);
    }
  }, [isItemModalVisible, itemForm]);

  // Reset category form when modal closes and pre-fill for editing
  useEffect(() => {
    if (!isCategoryModalVisible) {
      categoryForm.resetFields();
      setEditingCategory(null);
    } else if (editingCategory) {
      categoryForm.setFieldsValue({
        name: editingCategory.name,
        sortOrder: editingCategory.sortOrder,
        categoryKey: editingCategory.categoryKey,
      });
    }
  }, [isCategoryModalVisible, categoryForm, editingCategory]);

  // --- Category Actions ---

  const handleShowAddCategoryModal = () => {
    setEditingCategory(null);
    categoryForm.resetFields();
    categoryForm.setFieldsValue({ sortOrder: (categories.length + 1) * 10 });
    setIsCategoryModalVisible(true);
  };

  const handleShowEditCategoryModal = (category: ConfigCategory) => {
    setEditingCategory(category);
    setIsCategoryModalVisible(true);
  };

  const handleCategoryModalOk = async () => {
    try {
      const values = await categoryForm.validateFields();
      // Adjust result type to be more flexible or match hook return type more closely
      let result: {
        success: boolean;
        category?: any; // Use 'any' for now, or a more specific type matching the API response if known
        error?: string;
      };

      if (editingCategory) {
        const updateData: Pick<CategoryFormData, 'name' | 'sortOrder'> = {
          name: values.name,
          sortOrder:
            typeof values.sortOrder === 'number'
              ? values.sortOrder
              : editingCategory.sortOrder,
        };
        result = await updateCategory(editingCategory.id, updateData);
      } else {
        const formData: CategoryFormData = {
          name: values.name,
          categoryKey: values.categoryKey,
          sortOrder:
            typeof values.sortOrder === 'number' ? values.sortOrder : 0,
        };
        result = await createCategory(formData);
      }

      if (result.success) {
        message.success(editingCategory ? '分类更新成功' : '分类添加成功');
        setIsCategoryModalVisible(false);
      } else {
        message.error(
          result.error || (editingCategory ? '分类更新失败' : '分类添加失败'),
        );
      }
    } catch (errorInfo) {
      //console.log('Category form validation failed:', errorInfo);
      message.error('表单验证失败');
    }
  };

  const handleCategoryModalCancel = () => {
    setIsCategoryModalVisible(false);
  };

  const handleRemoveCategory = async (
    targetKey:
      | string
      | React.MouseEvent<Element, MouseEvent>
      | React.KeyboardEvent<Element>,
  ) => {
    if (typeof targetKey !== 'string') return;
    const result = await removeCategory(targetKey);
    if (result.success) {
      message.success('分类删除成功');
    } else {
      message.error(result.error || '分类删除失败');
    }
  };

  const onTabEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => {
    if (action === 'add') {
      handleShowAddCategoryModal();
    } else if (action === 'remove') {
      Modal.confirm({
        title: '确认删除分类?',
        content: '删除分类将同时删除其下的所有配置项，此操作不可恢复！',
        okText: '确认删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => handleRemoveCategory(targetKey),
      });
    }
  };

  // --- Config Item Actions ---

  const handleShowItemModal = (record?: ConfigItem) => {
    if (record) {
      setEditingRecord(record);
      // For editing, we still set the full key since the modal will display it as disabled
      itemForm.setFieldsValue({
        key: record.key,
        value: record.value, // Pass the raw value
        valueType: record.valueType,
        description: record.description,
      });
    } else {
      setEditingRecord(null);
      itemForm.resetFields();
      itemForm.setFieldsValue({ valueType: ValueTypes.TEXT }); // Default type
      // For new items, the key field will only contain the suffix part
    }
    setIsItemModalVisible(true);
  };

  const handleItemModalOk = async () => {
    if (!activeCategoryId) {
      message.error('请先选择一个配置分类');
      return;
    }
    try {
      const values = await itemForm.validateFields();
      let success = false;
      let errorMsg: string | undefined;

      if (editingRecord) {
        const updatePayload: Partial<ConfigItemFormData> = {
          // valueType and description are always included if changed by form
          valueType: values.valueType,
          description: values.description,
          // key is disabled for editing, so not included
        };

        // Special handling for password field to prevent submitting unchanged placeholders
        if (editingRecord.valueType === ValueTypes.PASSWORD) {
          const originalValueFromAPI = editingRecord.value; // This is the value fetched from API (e.g., '******')
          const currentValueInForm = values.value;

          // If original was placeholder ('******') and current form value is empty string,
          // it means user did not intend to change the password. So, don't send 'value'.
          if (originalValueFromAPI === '******' && currentValueInForm === '') {
            // Do not add 'value' to updatePayload
          } else {
            // User entered a new password, or wants to set it to empty string, or it wasn't a placeholder initially
            updatePayload.value = currentValueInForm;
          }
        } else {
          // For non-password fields, include the value if it has changed or always send it
          // Current hook logic expects 'value' if it's part of the form values.
          // To be more precise, one could compare with editingRecord.value,
          // but for simplicity, we'll send it if it's in 'values'.
          // The hook's editConfigItem will further process it.
          updatePayload.value = values.value;
        }

        const result = await editConfigItem(editingRecord.id, updatePayload);
        success = result.success;
        errorMsg = result.error;
      } else {
        // Creating new item
        // Find the current category to get its prefix
        const currentCategory = categories.find(
          (c) => c.id === activeCategoryId,
        );
        if (!currentCategory) {
          message.error('无法找到当前分类');
          return;
        }

        // The key field now only contains the suffix part (user input)
        // Combine the prefix with the user-entered suffix
        const fullKey = `${currentCategory.categoryKey}.${values.key}`;

        const createPayload: ConfigItemFormData = {
          key: fullKey,
          value: values.value,
          valueType: values.valueType,
          description: values.description,
          categoryId: activeCategoryId,
        };
        const result = await createConfigItem(createPayload);
        success = result.success;
        errorMsg = result.error;
      }

      if (success) {
        message.success(editingRecord ? '更新成功' : '创建成功');
        setIsItemModalVisible(false);
        actionRef.current?.reload(); // Reload table on success
      } else {
        message.error(errorMsg || (editingRecord ? '更新失败' : '创建失败'));
      }
    } catch (errorInfo) {
      //console.log('Item form validation failed:', errorInfo);
      message.error('表单验证失败');
    }
  };

  const handleItemModalCancel = () => {
    setIsItemModalVisible(false);
  };

  const handleDeleteItem = async (id: string) => {
    // activeCategoryId is no longer needed for removeConfigItem based on hook changes
    const result = await removeConfigItem(id); // Remove activeCategoryId argument
    if (result.success) {
      message.success('删除成功');
      actionRef.current?.reload(); // Reload table on success
    } else {
      message.error(result.error || '删除失败');
    }
  };

  // --- Table Columns moved to ConfigItemTable.tsx ---

  // --- Tab Rendering ---
  const renderTab = (cat: ConfigCategory) => (
    <span>
      {cat.name}
      <Button
        type="text"
        size="small"
        icon={<EditOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          handleShowEditCategoryModal(cat);
        }}
        style={{ marginLeft: 8, color: '#888' }}
        disabled={actionLoading}
      />
    </span>
  );

  return (
    <PageContainer title="配置管理">
      {/* Update Spin condition */}
      <Spin spinning={loadingCategories || actionLoading}>
        <Tabs
          type="editable-card"
          activeKey={activeCategoryId ?? undefined}
          onChange={setActiveCategoryId}
          onEdit={onTabEdit}
          hideAdd={actionLoading}
          tabBarExtraContent={
            actionLoading ? (
              <Spin size="small" style={{ marginRight: 16 }} />
            ) : null
          }
        >
          {categories.map((cat) => (
            <TabPane
              tab={renderTab(cat)}
              key={cat.id}
              closable={!actionLoading}
            >
              {activeCategoryId === cat.id && (
                <ConfigItemTable
                  actionRef={actionRef}
                  // dataSource={configItems} // Removed: Data fetched via request
                  // loading={loadingItems} // Removed: ProTable handles loading
                  request={loadConfigItems} // Added: Pass request function
                  actionLoading={actionLoading} // Keep for button states etc.
                  headerTitle={` 配置列表 - ${cat.name} (${cat.categoryKey})`}
                  onShowItemModal={handleShowItemModal}
                  onDeleteItem={handleDeleteItem}
                  editConfigItem={editConfigItem} // Pass editConfigItem function for inline editing
                />
              )}
            </TabPane>
          ))}
        </Tabs>
      </Spin>

      <CategoryModal
        visible={isCategoryModalVisible}
        onOk={handleCategoryModalOk}
        onCancel={handleCategoryModalCancel}
        confirmLoading={actionLoading}
        editingCategory={editingCategory}
        categoryForm={categoryForm}
      />

      <ConfigItemModal
        visible={isItemModalVisible}
        onOk={handleItemModalOk}
        onCancel={handleItemModalCancel}
        confirmLoading={actionLoading}
        editingRecord={editingRecord}
        itemForm={itemForm}
        activeCategoryId={activeCategoryId ?? undefined} // Convert null to undefined
        categories={categories}
      />
    </PageContainer>
  );
};

export default ConfigManagement;
