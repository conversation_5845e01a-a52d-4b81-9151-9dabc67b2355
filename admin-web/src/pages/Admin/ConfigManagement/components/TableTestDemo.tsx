import React, { useState } from 'react';
import { <PERSON>, Button, message } from 'antd';
import TableInput<PERSON>enderer from './TableInputRenderer';
import { TableStructure, TableColumnType } from '../types';

// Test demo component to verify table functionality
const TableTestDemo: React.FC = () => {
  const [tableValue, setTableValue] = useState<string>('');
  
  // Sample table data for testing
  const sampleTableData: TableStructure = {
    columns: [
      {
        id: 'col1',
        name: 'Product Name',
        key: 'product_name',
        dataType: TableColumnType.TEXT,
        required: true,
        sortable: true,
      },
      {
        id: 'col2',
        name: 'Price',
        key: 'price',
        dataType: TableColumnType.NUMBER,
        required: true,
        sortable: true,
      },
      {
        id: 'col3',
        name: 'In Stock',
        key: 'in_stock',
        dataType: TableColumnType.BOOLEAN,
        required: false,
        sortable: true,
      },
      {
        id: 'col4',
        name: 'Category',
        key: 'category',
        dataType: TableColumnType.SINGLE_SELECT,
        required: false,
        sortable: true,
        options: ['Electronics', 'Clothing', 'Books', 'Home & Garden'],
      },
    ],
    rows: [
      {
        id: 'row1',
        data: {
          product_name: 'Laptop',
          price: 999.99,
          in_stock: true,
          category: 'Electronics',
        },
      },
      {
        id: 'row2',
        data: {
          product_name: 'T-Shirt',
          price: 19.99,
          in_stock: true,
          category: 'Clothing',
        },
      },
      {
        id: 'row3',
        data: {
          product_name: 'Programming Book',
          price: 29.99,
          in_stock: false,
          category: 'Books',
        },
      },
    ],
    meta: {
      allowAddRows: true,
      allowDeleteRows: true,
      allowEditRows: true,
      allowAddColumns: true,
      allowDeleteColumns: true,
      allowEditColumns: true,
      showRowNumbers: true,
      paginated: false,
    },
  };

  const handleTableChange = (value: string) => {
    setTableValue(value);
    //console.log('Table value changed:', value);
  };

  const loadSampleData = () => {
    const jsonString = JSON.stringify(sampleTableData);
    setTableValue(jsonString);
    message.success('Sample data loaded');
  };

  const clearData = () => {
    setTableValue('');
    message.success('Data cleared');
  };

  const exportData = () => {
    if (tableValue) {
      try {
        const formatted = JSON.stringify(JSON.parse(tableValue), null, 2);
        navigator.clipboard.writeText(formatted);
        message.success('Table data copied to clipboard');
      } catch (error) {
        message.error('Failed to export data');
      }
    } else {
      message.warning('No data to export');
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <Card title="Table Field Type Test Demo" style={{ marginBottom: 20 }}>
        <div style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={loadSampleData} style={{ marginRight: 8 }}>
            Load Sample Data
          </Button>
          <Button onClick={clearData} style={{ marginRight: 8 }}>
            Clear Data
          </Button>
          <Button onClick={exportData}>
            Export Data
          </Button>
        </div>
        
        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
          <TableInputRenderer
            value={tableValue}
            onChange={handleTableChange}
          />
        </div>

        {tableValue && (
          <Card title="Current JSON Value" style={{ marginTop: 20 }} size="small">
            <pre style={{ 
              maxHeight: 200, 
              overflow: 'auto', 
              backgroundColor: '#f5f5f5', 
              padding: 12,
              fontSize: 12,
            }}>
              {JSON.stringify(JSON.parse(tableValue || '{}'), null, 2)}
            </pre>
          </Card>
        )}
      </Card>
    </div>
  );
};

export default TableTestDemo;