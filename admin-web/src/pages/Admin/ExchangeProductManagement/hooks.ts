import { ActionType } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { cloneDeep } from 'lodash-es';
import { useCallback, useRef, useState } from 'react';

// 模拟API服务，直到实际服务被导入
const productAPI = {
  getProducts: async (params: any) => {
    //console.log('模拟getProducts调用，参数:', params);
    return {
      code: 0,
      msg: '成功',
      data: { records: [], total: 0, current: 1, size: 10 },
    };
  },
  getProductDetail: async (params: any) => {
    //console.log('模拟getProductDetail调用，参数:', params);
    return { code: 0, msg: '成功', data: null };
  },
  createProduct: async (params: any) => {
    //console.log('模拟createProduct调用，参数:', params);
    return { code: 0, msg: '成功', data: { productId: 1 } };
  },
  updateProduct: async (params: any) => {
    //console.log('模拟updateProduct调用，参数:', params);
    return { code: 0, msg: '成功', data: { success: true } };
  },
  deleteProduct: async (params: any) => {
    //console.log('模拟deleteProduct调用，参数:', params);
    return { code: 0, msg: '成功', data: { success: true } };
  },
};

import { XpayapiInternalModelEntityExchangeProducts } from '@/api/model';
import { FORM_DEFAULT_VALUES } from './constants';
import {
  ApiResponse,
  EditingProduct,
  ProductFormValues,
  TableParams,
  // PaginatedResponse, // Removed unused import
  XpayapiApiSystemV1CreateProductRes,
  // XpayapiApiSystemV1DeleteProductRes, // Removed unused import
  XpayapiApiSystemV1UpdateProductRes,
} from './types';
import { formatProductFormValues, formatRequestParams } from './utils';

/**
 * 检查API响应是否成功
 */
const isSuccessResponse = <T>(response: ApiResponse<T>): boolean => {
  return response && response.code === 0;
};

/**
 * 兑换产品管理的Hook
 */
export const useProduct = () => {
  const [form] = Form.useForm<ProductFormValues>();

  // 表格加载状态
  const [loading, setLoading] = useState<boolean>(false);

  // 表单提交加载状态
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 模态框可见状态
  const [visible, setVisible] = useState<boolean>(false);

  // 产品数据
  const [products, setProducts] = useState<
    XpayapiInternalModelEntityExchangeProducts[]
  >([]);

  // 当前正在编辑的产品
  const [editingProduct, setEditingProduct] = useState<EditingProduct | null>(
    null,
  );

  // 删除加载状态（按产品ID跟踪）
  const [deleteLoading, setDeleteLoading] = useState<Record<number, boolean>>(
    {},
  );

  // 获取详情加载状态
  const [fetchDetailLoading, setFetchDetailLoading] = useState<boolean>(false);

  // 表格操作的引用
  const actionRef = useRef<ActionType>(null);

  // 分页状态
  const [total, setTotal] = useState<number>(0);

  /**
   * 处理API错误，改进了错误信息提取
   */
  const handleApiError = useCallback((error: any, customMsg?: string) => {
    console.error('API错误:', error);

    let errorMessage = customMsg || '操作失败，请重试';

    // 从不同可能的错误结构中提取错误信息
    if (error?.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error?.msg) {
      errorMessage = error.msg;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    message.error(errorMessage);
  }, []);

  /**
   * Fetch products from API with improved pagination handling
   */
  const fetchProducts = useCallback(
    async (params: TableParams = {}) => {
      try {
        setLoading(true);
        const formattedParams = formatRequestParams(params);
        const response = await productAPI.getProducts(formattedParams);

        if (isSuccessResponse(response) && response.data) {
          const { records, total } = response.data;

          // Ensure records is always an array even if API returns null
          const safeRecords = records || [];

          setProducts(safeRecords);
          setTotal(total || 0);

          return {
            data: safeRecords,
            success: true,
            total: total || 0,
          };
        } else {
          handleApiError(response, 'Failed to fetch products');
          return {
            data: [],
            success: false,
            total: 0,
          };
        }
      } catch (error) {
        handleApiError(error);
        return {
          data: [],
          success: false,
          total: 0,
        };
      } finally {
        setLoading(false);
      }
    },
    [handleApiError],
  );

  /**
   * Open form modal to add new product
   */
  const handleAdd = useCallback(() => {
    setEditingProduct(null);
    form.resetFields();
    form.setFieldsValue(cloneDeep(FORM_DEFAULT_VALUES));
    setVisible(true);
  }, [form]);

  /**
   * Open form modal to edit product with improved error handling
   */
  const handleEdit = useCallback(
    async (product: XpayapiInternalModelEntityExchangeProducts) => {
      const id = product.productId;
      if (!id) {
        message.error('Invalid product ID');
        return;
      }

      try {
        setFetchDetailLoading(true);
        const response = await productAPI.getProductDetail({ id });

        if (isSuccessResponse(response) && response.data) {
          setEditingProduct({
            id,
            product: response.data,
          });
          // Set form values based on product data here
          form.setFieldsValue({
            baseTokenId: product.baseTokenId,
            quoteTokenId: product.quoteTokenId,
            symbol: product.symbol,
            // Add other fields as needed
          });
          setVisible(true);
        } else {
          handleApiError(
            response,
            `Failed to fetch details for product: ${product.symbol || id}`,
          );
        }
      } catch (error) {
        handleApiError(error);
      } finally {
        setFetchDetailLoading(false);
      }
    },
    [handleApiError, form],
  );

  /**
   * Handle form submission for create/update with improved validation
   */
  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      const formattedValues = formatProductFormValues(values);

      let response: ApiResponse<
        XpayapiApiSystemV1CreateProductRes | XpayapiApiSystemV1UpdateProductRes
      >;

      if (editingProduct?.id) {
        // Update existing product
        response = await productAPI.updateProduct({
          id: editingProduct.id,
          ...formattedValues,
        });
      } else {
        // Create new product
        response = await productAPI.createProduct(formattedValues);
      }

      if (isSuccessResponse(response)) {
        message.success(
          editingProduct
            ? 'Product updated successfully'
            : 'Product created successfully',
        );
        setVisible(false);

        // Refresh table data
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        handleApiError(response, response.msg || 'Failed to save product');
      }
    } catch (error) {
      handleApiError(error);
    } finally {
      setSubmitting(false);
    }
  }, [editingProduct, form, handleApiError, actionRef]);

  /**
   * Delete product by ID with improved loading state tracking
   */
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        // Set loading state for specific product
        setDeleteLoading((prev) => ({ ...prev, [id]: true }));

        const response = await productAPI.deleteProduct({ id });

        if (isSuccessResponse(response)) {
          message.success('Product deleted successfully');

          // Refresh table data
          if (actionRef.current) {
            actionRef.current.reload();
          }
        } else {
          handleApiError(response, response.msg || 'Failed to delete product');
        }
      } catch (error) {
        handleApiError(error);
      } finally {
        // Clear loading state for specific product
        setDeleteLoading((prev) => ({ ...prev, [id]: false }));
      }
    },
    [handleApiError, actionRef],
  );

  /**
   * Handle modal cancel
   */
  const handleCancel = useCallback(() => {
    setVisible(false);
    form.resetFields();
  }, [form]);

  return {
    form,
    actionRef,
    loading,
    submitting,
    visible,
    deleteLoading,
    fetchDetailLoading,
    products,
    total,
    editingProduct,
    fetchProducts,
    handleAdd,
    handleEdit,
    handleDelete,
    handleSubmit,
    handleCancel,
  };
};
