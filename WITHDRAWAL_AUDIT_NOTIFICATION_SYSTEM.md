# Real-time Withdrawal Audit Notification System

## Overview
This system provides real-time notifications for pending withdrawal audit requests with audio alerts, browser tab coordination, and automatic polling.

## Features Implemented

### Backend (Go + GoFrame)
1. **API Endpoint**: `GET /api/system/user-withdraws/pending-audits`
   - Queries `user_withdraws` table for records with `audit_status = 2`
   - Returns count and details of pending requests with timestamps
   - Implements 500ms Redis caching for performance

2. **Database Optimization**
   - Added indexes on `audit_status` column for faster queries
   - Composite index on `(audit_status, created_at)` for sorted queries

### Frontend (React + TypeScript)
1. **Notification Component** (`/src/components/WithdrawAuditNotifications/`)
   - Fixed position on right side of screen
   - Stacked notifications with individual close buttons
   - Shows Chinese message: "您有等待审核的提现申请，请您及时处理！"
   - Displays withdrawal details and waiting time

2. **Polling Mechanism**
   - 1-second interval polling when page is loaded
   - Only polls when browser tab is visible
   - Exponential backoff on errors (1s → 2s → 4s → 8s → 16s max)

3. **Audio Alerts**
   - Continuous looping audio when new requests detected
   - Stops only when user closes notifications
   - Handles browser autoplay policies gracefully

4. **Browser Tab Coordination**
   - Uses BroadcastChannel API (with localStorage fallback)
   - Leader election ensures only one tab polls at a time
   - Automatic leadership handoff when tabs close/open
   - 500ms heartbeat interval for leader detection

5. **Visual Indicators**
   - Updates browser title with pending count
   - Changes favicon to alert version (when available)

## File Structure

### Backend Files
```
/admin-api/
├── api/system/v1/user_withdraw.go                    # API definitions
├── internal/controller/system/system_v1_get_pending_audits.go  # Controller
├── internal/logic/system/v1/user_withdraw_fixed.go   # Business logic
├── internal/service/system.go                        # Service interface
└── manifest/sql/add_audit_status_index.sql          # Database indexes
```

### Frontend Files
```
/admin-web/src/
├── components/
│   ├── WithdrawAuditNotifications/
│   │   ├── index.tsx              # Main notification component
│   │   ├── useTabCoordinator.ts   # Tab coordination hook
│   │   ├── AudioController.ts     # Audio playback controller
│   │   └── styles.less           # Component styles
│   └── GlobalWrapper/
│       └── index.tsx             # Global component wrapper
├── api/interfaces/system-user-withdraw/
│   └── pending-audits.ts         # API interface
└── app.tsx                       # App configuration with wrapper
```

## Usage

1. **Audio File**: Place an MP3 notification sound at:
   `/admin-web/public/assets/sounds/notification-alert.mp3`

2. **Favicon**: Create an alert version of favicon at:
   `/admin-web/public/favicon-alert.ico`

3. **Database Migration**: Run the SQL script to add indexes:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_user_withdraws_audit_status ON user_withdraws(audit_status);
   CREATE INDEX IF NOT EXISTS idx_user_withdraws_audit_status_created_at ON user_withdraws(audit_status, created_at DESC);
   ```

## Configuration

### Backend
- Cache TTL: 500ms (configurable in `GetPendingAudits` function)
- Max results: 100 pending withdrawals per request

### Frontend
- Polling interval: 2000ms (2 seconds)
- Leader heartbeat: 500ms
- Leader timeout: 1500ms
- Error retry delays: [1s, 2s, 4s, 8s, 16s]

## Security Considerations

1. **Authentication**: API endpoint requires valid JWT token
2. **Rate Limiting**: Should be implemented at API gateway level
3. **XSS Prevention**: All user data is properly escaped
4. **HTTPS**: Ensure all API calls use HTTPS in production

## Browser Compatibility

- Modern browsers with BroadcastChannel API support
- Fallback to localStorage for older browsers
- Audio autoplay may require user interaction in some browsers

## Future Enhancements

1. WebSocket support for true real-time updates
2. Configurable notification sounds and volumes
3. Email/SMS notifications for critical withdrawals
4. Detailed audit logs for notification interactions
5. Performance metrics and monitoring dashboard