package v1

import (
	"admin-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取提现记录列表 ---

// ListUserWithdrawsReq defines the request structure for getting the user withdraws list.
type ListUserWithdrawsReq struct {
	g.Meta `path:"/user-withdraws" method:"get" tags:"SystemUserWithdraw" summary:"获取提现记录列表"`
	common.PageRequest
	UserId           *uint64  `json:"userId" dc:"用户ID"`
	Account          *string  `json:"account" dc:"用户账号"`
	Username         *string  `json:"username" dc:"用户名"`
	TokenId          *uint    `json:"tokenId" dc:"币种ID"`
	Symbol           *string  `json:"symbol" dc:"币种符号"`
	Chain            *string  `json:"chain" dc:"链名称"`
	Address          *string  `json:"address" dc:"提币地址"`
	OrderNo          *string  `json:"orderNo" dc:"订单号"`
	AuditStatus      *uint    `json:"auditStatus" dc:"审核状态: 0-全部, 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝" v:"in:0,1,2,3,4"`
	ProcessingStatus *uint    `json:"processingStatus" dc:"处理状态: 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败" v:"in:0,1,2,3,4,5"`
	DateRange        string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	AmountMin        *float64 `json:"amountMin" dc:"最小金额"`
	AmountMax        *float64 `json:"amountMax" dc:"最大金额"`

	// 新增：三级代理模糊查询
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称 (模糊搜索)"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称 (模糊搜索)"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称 (模糊搜索)"`

	// 新增：telegram查询条件
	TelegramId       string `json:"telegramId" dc:"Telegram ID (模糊搜索)"`
	TelegramUsername string `json:"telegramUsername" dc:"Telegram用户名 (模糊搜索)"`
	FirstName        string `json:"firstName" dc:"名字 (模糊搜索)"`

	// 新增：法币提现查询条件
	FiatType         string `json:"fiatType" dc:"法币提现类型: alipay_account-支付宝账号, alipay_qr-支付宝二维码, wechat_qr-微信二维码" v:"in:alipay_account,alipay_qr,wechat_qr"`
	RecipientName    string `json:"recipientName" dc:"法币收款人姓名 (模糊搜索)"`
	RecipientAccount string `json:"recipientAccount" dc:"法币收款账户 (模糊搜索)"`
}

// ListUserWithdrawsRes defines the response structure for getting the user withdraws list.
type ListUserWithdrawsRes struct {
	Page common.PageResponse      `json:"page" dc:"分页信息"`
	Data []*UserWithdrawsListItem `json:"data" dc:"提现记录列表"`
}

// UserWithdrawsListItem defines the structure for a user withdraw list item.
type UserWithdrawsListItem struct {
	UserWithdrawsId    uint        `json:"userWithdrawsId" dc:"提现记录ID"`
	UserId             uint64      `json:"userId" dc:"用户ID"`
	Account            string      `json:"account" dc:"用户账号"`
	Nickname           string      `json:"nickname" dc:"用户昵称"`
	TokenId            uint        `json:"tokenId" dc:"币种ID"`
	Symbol             string      `json:"symbol" dc:"币种符号"`
	Chain              string      `json:"chain" dc:"链名称"`
	WalletId           string      `json:"walletId" dc:"钱包ID"`
	OrderNo            string      `json:"orderNo" dc:"订单号"`
	Address            string      `json:"address" dc:"提币地址"`
	RecipientName      string      `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount   string      `json:"recipientAccount" dc:"法币收款账户"`
	RecipientQrcode    string      `json:"recipientQrcode" dc:"法币收款二维码"`
	FiatType           string      `json:"fiatType" dc:"法币提现类型"`
	Amount             float64     `json:"amount" dc:"申请提现金额"`
	HandlingFee        float64     `json:"handlingFee" dc:"手续费"`
	ActualAmount       float64     `json:"actualAmount" dc:"实际到账金额"`
	AuditStatus        uint        `json:"auditStatus" dc:"审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝"`
	ProcessingStatus   uint        `json:"processingStatus" dc:"处理状态: 0-无, 1-自动放币处理中, 2-处理中(待冷钱包), 3-待人工转账, 4-成功, 5-失败"`
	TxHash             string      `json:"txHash" dc:"交易哈希"`
	UserRemark         string      `json:"userRemark" dc:"用户备注"`
	AdminRemark        string      `json:"adminRemark" dc:"管理员备注"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`
	CheckedAt          *gtime.Time `json:"checkedAt" dc:"审核时间"`
	ProcessingAt       *gtime.Time `json:"processingAt" dc:"处理时间"`
	CompletedAt        *gtime.Time `json:"completedAt" dc:"完成时间"`
	NotificationSent   uint        `json:"notificationSent" dc:"是否已发送通知: 0-未发送, 1-已发送"`
	NotificationSentAt *gtime.Time `json:"notificationSentAt" dc:"通知发送时间"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`

	// 新增：显示控制字段
	ShowApprovalButton bool `json:"showApprovalButton" dc:"是否显示审核按钮"`
	ShowTransferButton bool `json:"showTransferButton" dc:"是否显示转账结果按钮"`
}

// --- 获取提现记录详情 ---

// GetUserWithdrawDetailReq defines the request structure for getting a user withdraw detail.
type GetUserWithdrawDetailReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}" method:"get" tags:"SystemUserWithdraw" summary:"获取提现记录详情"`
	UserWithdrawsId uint `json:"withdrawId"  dc:"提现记录ID"`
}

// GetUserWithdrawDetailRes defines the response structure for getting a user withdraw detail.
type GetUserWithdrawDetailRes struct {
	Data *UserWithdrawDetailItem `json:"data" dc:"提现记录详情"`
}

// UserWithdrawDetailItem defines the structure for a user withdraw detail item.
type UserWithdrawDetailItem struct {
	UserWithdrawsId    uint        `json:"userWithdrawsId" dc:"提现记录ID"`
	UserId             uint64      `json:"userId" dc:"用户ID"`
	Account            string      `json:"account" dc:"用户账号"`
	Nickname           string      `json:"nickname" dc:"用户昵称"`
	TokenId            uint        `json:"tokenId" dc:"币种ID"`
	Symbol             string      `json:"symbol" dc:"币种符号"`
	Chain              string      `json:"chain" dc:"链名称"`
	WalletId           string      `json:"walletId" dc:"钱包ID"`
	OrderNo            string      `json:"orderNo" dc:"订单号"`
	Address            string      `json:"address" dc:"提币地址"`
	RecipientName      string      `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount   string      `json:"recipientAccount" dc:"法币收款账户"`
	RecipientQrcode    string      `json:"recipientQrcode" dc:"法币收款二维码"`
	FiatType           string      `json:"fiatType" dc:"法币提现类型"`
	Amount             float64     `json:"amount" dc:"申请提现金额"`
	HandlingFee        float64     `json:"handlingFee" dc:"手续费"`
	ActualAmount       float64     `json:"actualAmount" dc:"实际到账金额"`
	AuditStatus        uint        `json:"auditStatus" dc:"审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝"`
	ProcessingStatus   uint        `json:"processingStatus" dc:"处理状态: 0-无, 1-自动放币处理中, 2-处理中(待冷钱包), 3-待人工转账, 4-成功, 5-失败"`
	RefuseReasonZh     string      `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn     string      `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	TxHash             string      `json:"txHash" dc:"交易哈希"`
	ErrorMessage       string      `json:"errorMessage" dc:"错误信息"`
	UserRemark         string      `json:"userRemark" dc:"用户备注"`
	AdminRemark        string      `json:"adminRemark" dc:"管理员备注"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`
	CheckedAt          *gtime.Time `json:"checkedAt" dc:"审核时间"`
	ProcessingAt       *gtime.Time `json:"processingAt" dc:"处理时间"`
	CompletedAt        *gtime.Time `json:"completedAt" dc:"完成时间"`
	NotificationSent   uint        `json:"notificationSent" dc:"是否已发送通知: 0-未发送, 1-已发送"`
	NotificationSentAt *gtime.Time `json:"notificationSentAt" dc:"通知发送时间"`

	// 新增：三级代理信息
	FirstAgentName  string `json:"firstAgentName" dc:"一级代理名称"`
	SecondAgentName string `json:"secondAgentName" dc:"二级代理名称"`
	ThirdAgentName  string `json:"thirdAgentName" dc:"三级代理名称"`

	// 新增：主备份账户telegram信息
	TelegramId       string `json:"telegramId" dc:"主Telegram ID"`
	TelegramUsername string `json:"telegramUsername" dc:"主Telegram用户名"`
	FirstName        string `json:"firstName" dc:"主备份账户名字"`
}

// --- 审核提现记录 ---

// ReviewUserWithdrawReq defines the request structure for reviewing a user withdraw.
type ReviewUserWithdrawReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/review" method:"put" tags:"SystemUserWithdraw" summary:"审核提现记录"`
	UserWithdrawsId uint   `json:"withdrawId"  dc:"提现记录ID"`
	Action          string `json:"action" v:"required|in:approve,reject#操作类型不能为空|操作类型必须是approve或reject" dc:"操作类型: approve-通过, reject-拒绝"`
	RefuseReasonZh  string `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn  string `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// ReviewUserWithdrawRes defines the response structure for reviewing a user withdraw.
type ReviewUserWithdrawRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新提现记录状态 ---

// UpdateUserWithdrawStatusReq defines the request structure for updating a user withdraw status.
type UpdateUserWithdrawStatusReq struct {
	g.Meta           `path:"/user-withdraws/{withdrawId}/status" method:"put" tags:"SystemUserWithdraw" summary:"更新提现记录状态"`
	UserWithdrawsId  uint   `json:"withdrawId"  dc:"提现记录ID"`
	AuditStatus      uint   `json:"auditStatus" v:"required|in:1,2,3,4#审核状态不能为空|审核状态值无效" dc:"审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝"`
	ProcessingStatus uint   `json:"processingStatus" v:"required|in:0,1,2,3,4,5#处理状态不能为空|处理状态值无效" dc:"处理状态: 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败"`
	TxHash           string `json:"txHash" dc:"交易哈希"`
	ErrorMessage     string `json:"errorMessage" dc:"错误信息"`
	AdminRemark      string `json:"adminRemark" dc:"管理员备注"`
}

// UpdateUserWithdrawStatusRes defines the response structure for updating a user withdraw status.
type UpdateUserWithdrawStatusRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 审批提现申请 ---

// ApproveUserWithdrawReq defines the request structure for approving a user withdraw.
type ApproveUserWithdrawReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/approve" method:"post" tags:"SystemUserWithdraw" summary:"审批提现申请"`
	UserWithdrawsId uint   `json:"withdrawId" dc:"提现记录ID"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// ApproveUserWithdrawRes defines the response structure for approving a user withdraw.
type ApproveUserWithdrawRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 拒绝提现申请 ---

// RejectUserWithdrawReq defines the request structure for rejecting a user withdraw.
type RejectUserWithdrawReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/reject" method:"post" tags:"SystemUserWithdraw" summary:"拒绝提现申请"`
	UserWithdrawsId uint   `json:"withdrawId" dc:"提现记录ID"`
	RefuseReason    string `json:"refuseReason" dc:"拒绝原因"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// RejectUserWithdrawRes defines the response structure for rejecting a user withdraw.
type RejectUserWithdrawRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新转账结果 ---

// UpdateTransferResultReq defines the request structure for updating transfer result.
type UpdateTransferResultReq struct {
	g.Meta          `path:"/user-withdraws/{withdrawId}/transfer-result" method:"put" tags:"SystemUserWithdraw" summary:"更新转账结果"`
	UserWithdrawsId uint   `json:"withdrawId" dc:"提现记录ID"`
	Result          string `json:"result" v:"required|in:success,failed#结果不能为空|结果必须是success或failed" dc:"转账结果: success-成功, failed-失败"`
	TxHash          string `json:"txHash" dc:"交易哈希"`
	ErrorMessage    string `json:"errorMessage" dc:"错误信息"`
	AdminRemark     string `json:"adminRemark" dc:"管理员备注"`
}

// UpdateTransferResultRes defines the response structure for updating transfer result.
type UpdateTransferResultRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// --- 获取待审核提现记录 ---

// GetPendingAuditsReq defines the request structure for getting pending audit withdrawals.
type GetPendingAuditsReq struct {
	g.Meta `path:"/user-withdraws/pending-audits" method:"get" tags:"SystemUserWithdraw" summary:"获取待审核提现记录"`
}

// GetPendingAuditsRes defines the response structure for getting pending audit withdrawals.
type GetPendingAuditsRes struct {
	Count       int                    `json:"count" dc:"待审核数量"`
	Withdraws   []*PendingWithdrawInfo `json:"withdraws" dc:"待审核提现列表"`
	HasNewItems bool                   `json:"hasNewItems" dc:"是否有新记录"`
}

// PendingWithdrawInfo defines the structure for pending withdrawal information.
type PendingWithdrawInfo struct {
	UserWithdrawsId uint        `json:"userWithdrawsId" dc:"提现记录ID"`
	UserId          uint64      `json:"userId" dc:"用户ID"`
	Account         string      `json:"account" dc:"用户账号"`
	Nickname        string      `json:"nickname" dc:"用户昵称"`
	Symbol          string      `json:"symbol" dc:"币种符号"`
	Amount          float64     `json:"amount" dc:"申请提现金额"`
	ActualAmount    float64     `json:"actualAmount" dc:"实际到账金额"`
	CreatedAt       *gtime.Time `json:"createdAt" dc:"创建时间"`
	WaitingTime     string      `json:"waitingTime" dc:"等待时间"`
	OrderNo         string      `json:"orderNo" dc:"订单号"`
	Address         string      `json:"address" dc:"提币地址"`
	Chain           string      `json:"chain" dc:"链名称"`
}
