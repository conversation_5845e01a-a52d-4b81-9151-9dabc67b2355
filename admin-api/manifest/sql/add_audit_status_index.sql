-- Add index on audit_status for better query performance on pending withdrawals
-- This index will significantly improve the performance of queries filtering by audit_status = 2

CREATE INDEX IF NOT EXISTS idx_user_withdraws_audit_status ON user_withdraws(audit_status);

-- Composite index for audit_status and created_at for sorting
CREATE INDEX IF NOT EXISTS idx_user_withdraws_audit_status_created_at ON user_withdraws(audit_status, created_at DESC);

-- Add index on notification_sent for future notification tracking
CREATE INDEX IF NOT EXISTS idx_user_withdraws_notification_sent ON user_withdraws(notification_sent);