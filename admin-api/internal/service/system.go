package service

import (
	"context"
	"math/big"
	"sync"

	v1 "admin-api/api/system/v1"
	"admin-api/internal/model/entity"

	// Import config service interfaces
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/shopspring/decimal"
)

var (
	SystemServiceInstance SystemService
	SystemOnce            sync.Once
)

// RegisterSystem 注册系统服务
func RegisterSystem(serviceImpl SystemService) {
	SystemOnce.Do(func() {
		SystemServiceInstance = serviceImpl
	})
}

// SystemService 系统服务接口
type SystemService interface {

	// -- Auth API --
	// 获取图形验证码
	// GetCaptcha(ctx context.Context, req *v1.GetCaptchaReq) (res *v1.GetCaptchaRes, err error)
	// 登录
	Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error)
	// 获取管理员信息
	GetAdminInfo(ctx context.Context, req *v1.GetAdminInfoReq) (res *v1.GetAdminInfoRes, err error)
	// 修改个人信息
	UpdateAdminInfo(ctx context.Context, req *v1.UpdateAdminInfoReq) (res *v1.UpdateAdminInfoRes, err error)

	// GetLoginLogList 获取登录日志列表
	GetLoginLogList(ctx context.Context, req *v1.GetLoginLogListReq) (res *v1.GetLoginLogListRes, err error)

	// GetLoginLogDetail 获取登录日志详情
	GetLoginLogDetail(ctx context.Context, req *v1.GetLoginLogDetailReq) (res *v1.GetLoginLogDetailRes, err error)

	// GetOperationLogList 获取操作日志列表
	GetOperationLogList(ctx context.Context, req *v1.GetOperationLogListReq) (res *v1.GetOperationLogListRes, err error)

	// GetOperationLogDetail 获取操作日志详情
	GetOperationLogDetail(ctx context.Context, req *v1.GetOperationLogDetailReq) (res *v1.GetOperationLogDetailRes, err error)

	// GetIpAccessList 获取IP访问列表
	GetIpAccessList(ctx context.Context, req *v1.GetIpAccessListReq) (res *v1.GetIpAccessListRes, err error)

	// AddIpAccessList 添加IP到访问列表
	AddIpAccessList(ctx context.Context, req *v1.AddIpAccessListReq) (res *v1.AddIpAccessListRes, err error)

	// DeleteIpAccessList 从访问列表删除IP
	DeleteIpAccessList(ctx context.Context, req *v1.DeleteIpAccessListReq) (res *v1.DeleteIpAccessListRes, err error)

	// PatchIpAccessList 更新IP访问控制状态 (启用/禁用)
	PatchIpAccessList(ctx context.Context, req *v1.PatchIpAccessListReq) (res *v1.PatchIpAccessListRes, err error)

	// GetMenuList 获取菜单列表
	GetMenuList(ctx context.Context, req *v1.GetMenuListReq) (res *v1.GetMenuListRes, err error)

	// GetMenu 获取菜单详情
	// GetMenu(ctx context.Context, req *v1.GetMenuReq) (res *v1.GetMenuRes, err error)

	// AddMenu 新增菜单
	AddMenu(ctx context.Context, req *v1.AddMenuReq) (res *v1.AddMenuRes, err error)

	// EditMenu 编辑菜单
	EditMenu(ctx context.Context, req *v1.EditMenuReq) (res *v1.EditMenuRes, err error)

	// DeleteMenu 删除菜单
	DeleteMenu(ctx context.Context, req *v1.DeleteMenuReq) (res *v1.DeleteMenuRes, err error)

	// GetRoleList 获取角色列表
	GetRoleList(ctx context.Context, req *v1.GetRoleListReq) (res *v1.GetRoleListRes, err error)

	// GetRole 获取角色详情
	GetRole(ctx context.Context, req *v1.GetRoleReq) (res *v1.GetRoleRes, err error)

	// AddRole 新增角色
	AddRole(ctx context.Context, req *v1.AddRoleReq) (res *v1.AddRoleRes, err error)

	// EditRole 编辑角色
	EditRole(ctx context.Context, req *v1.EditRoleReq) (res *v1.EditRoleRes, err error)

	// DeleteRole 删除角色
	DeleteRole(ctx context.Context, req *v1.DeleteRoleReq) (res *v1.DeleteRoleRes, err error)

	// GetRoleMenuIds 获取角色关联的菜单ID列表
	GetRoleMenuIds(ctx context.Context, req *v1.GetRoleMenuIdsReq) (res *v1.GetRoleMenuIdsRes, err error)

	// AssignRoleMenus 分配角色菜单
	AssignRoleMenus(ctx context.Context, req *v1.AssignRoleMenusReq) (res *v1.AssignRoleMenusRes, err error)

	// UpdateRoleDataScope 更新角色数据范围
	UpdateRoleDataScope(ctx context.Context, req *v1.UpdateRoleDataScopeReq) (res *v1.UpdateRoleDataScopeRes, err error)

	// --- Notice Admin API ---
	GetAdminNoticeList(ctx context.Context, req *v1.GetAdminNoticeListReq) (res *v1.GetAdminNoticeListRes, err error)
	// GetAdminNotice 获取单个公告详情
	GetAdminNotice(ctx context.Context, req *v1.GetAdminNoticeReq) (res *v1.GetAdminNoticeRes, err error)
	// AddAdminNotice 新增公告
	AddAdminNotice(ctx context.Context, req *v1.AddAdminNoticeReq) (res *v1.AddAdminNoticeRes, err error)
	// EditAdminNotice 编辑公告
	EditAdminNotice(ctx context.Context, req *v1.EditAdminNoticeReq) (res *v1.EditAdminNoticeRes, err error)
	// DeleteAdminNotice 删除公告
	DeleteAdminNotice(ctx context.Context, req *v1.DeleteAdminNoticeReq) (res *v1.DeleteAdminNoticeRes, err error)
	// GetAdminNoticeReadStatus 获取公告阅读状态
	GetAdminNoticeReadStatus(ctx context.Context, req *v1.GetAdminNoticeReadStatusReq) (res *v1.GetAdminNoticeReadStatusRes, err error)
	// GetMemberListForNotice 获取公告成员列表
	GetMemberListForNotice(ctx context.Context, req *v1.GetMemberListForNoticeReq) (res *v1.GetMemberListForNoticeRes, err error)

	// --- Notice User API ---
	// GetMyNoticeList 获取我的公告列表
	GetMyNoticeList(ctx context.Context, req *v1.GetMyNoticeListReq) (res *v1.GetMyNoticeListRes, err error)
	// MarkNoticeRead 标记公告已读
	MarkNoticeRead(ctx context.Context, req *v1.MarkNoticeReadReq) (res *v1.MarkNoticeReadRes, err error)
	// GetMyUnreadNoticeCount 获取未读公告数量
	GetMyUnreadNoticeCount(ctx context.Context, req *v1.GetMyUnreadNoticeCountReq) (res *v1.GetMyUnreadNoticeCountRes, err error)

	// --- Member Admin API ---
	GetMemberList(ctx context.Context, req *v1.GetMemberListReq) (res *v1.GetMemberListRes, err error)
	// GetMember 获取单个成员详情
	GetMember(ctx context.Context, req *v1.GetMemberReq) (res *v1.GetMemberRes, err error)
	// AddMember 新增成员
	AddMember(ctx context.Context, req *v1.AddMemberReq) (res *v1.AddMemberRes, err error)
	// EditMember 编辑成员
	EditMember(ctx context.Context, req *v1.EditMemberReq) (res *v1.EditMemberRes, err error)
	// DeleteMember 删除成员
	DeleteMember(ctx context.Context, req *v1.DeleteMemberReq) (res *v1.DeleteMemberRes, err error)
	// UpdateMemberStatus 更新成员状态
	UpdateMemberStatus(ctx context.Context, req *v1.UpdateMemberStatusReq) (res *v1.UpdateMemberStatusRes, err error)
	// ResetMemberPassword 重置成员密码
	ResetMemberPassword(ctx context.Context, req *v1.ResetMemberPasswordReq) (res *v1.ResetMemberPasswordRes, err error)

	//--- Agent Admin API ---
	// AddAgent 添加代理
	AddAgent(ctx context.Context, req *v1.AddAgentReq) (res *v1.AddAgentRes, err error)
	// GetAgentList 获取代理列表 (包含导出逻辑判断)
	GetAgentList(ctx context.Context, req *v1.GetAgentListReq) (res *v1.GetAgentListRes, err error)
	// GetAgent 获取单个代理详情
	GetAgent(ctx context.Context, req *v1.GetAgentReq) (res *v1.GetAgentRes, err error)
	// EditAgent 编辑代理基础信息
	EditAgent(ctx context.Context, req *v1.EditAgentReq) (res *v1.EditAgentRes, err error)
	// DeleteAgent 批量软删除代理 (包含删除下级逻辑)
	DeleteAgent(ctx context.Context, req *v1.DeleteAgentReq) (res *v1.DeleteAgentRes, err error)
	// UpdateAgentStatus 批量更新代理状态
	UpdateAgentStatus(ctx context.Context, req *v1.UpdateAgentStatusReq) (res *v1.UpdateAgentStatusRes, err error)
	// UpdateAgentPassword 修改指定代理密码
	UpdateAgentPassword(ctx context.Context, req *v1.UpdateAgentPasswordReq) (res *v1.UpdateAgentPasswordRes, err error)
	// ResetAgent2FA 重置指定代理的 Google Authenticator
	ResetAgent2FA(ctx context.Context, req *v1.ResetAgent2FAReq) (res *v1.ResetAgent2FARes, err error)
	// GetAgentWhitelist 获取指定代理的 IP 白名单
	GetAgentWhitelist(ctx context.Context, req *v1.GetAgentWhitelistReq) (res *v1.GetAgentWhitelistRes, err error)
	// AddAgentWhitelist 为指定代理添加 IP 白名单
	AddAgentWhitelist(ctx context.Context, req *v1.AddAgentWhitelistReq) (res *v1.AddAgentWhitelistRes, err error)
	// DeleteAgentWhitelist 删除指定代理的 IP 白名单
	DeleteAgentWhitelist(ctx context.Context, req *v1.DeleteAgentWhitelistReq) (res *v1.DeleteAgentWhitelistRes, err error)

	// --- Token Admin API ---
	// GetTokenList 获取令牌列表
	GetTokenList(ctx context.Context, req *v1.GetTokenListReq) (*v1.GetTokenListRes, error)
	// GetToken 获取单个令牌详情
	GetTokenDetail(ctx context.Context, req *v1.GetTokenDetailReq) (*v1.GetTokenDetailRes, error)
	// AddToken 添加令牌
	CreateToken(ctx context.Context, req *v1.CreateTokenReq) (*v1.CreateTokenRes, error)
	// EditToken 编辑令牌
	UpdateToken(ctx context.Context, req *v1.UpdateTokenReq) (*v1.UpdateTokenRes, error)
	// DeleteToken 删除令牌
	DeleteToken(ctx context.Context, req *v1.DeleteTokenReq) (*v1.DeleteTokenRes, error)

	// --- Wallet Service API ---
	// GetWalletBalance 获取钱包余额
	GetWalletBalance(ctx context.Context, req *v1.GetWalletBalanceReq) (res *v1.GetWalletBalanceRes, err error)
	// GetOrCreateWallet 确保钱包存在，如果不存在则创建。返回钱包详情。
	GetOrCreateWallet(ctx context.Context, userId uint32, tokenId uint32) (*entity.Wallets, error)

	// IncreaseBalance 增加可用余额 (使用存储的 int64 金额)
	IncreaseBalance(ctx context.Context, userId uint32, tokenId uint32, amount decimal.Decimal, changeReason string) error

	// --- Transaction Admin API ---
	// ListAdminTransactions 获取后台交易记录列表
	ListAdminTransactions(ctx context.Context, req *v1.ListAdminTransactionsReq) (res *v1.ListAdminTransactionsRes, err error)

	// --- Referral Commission Admin API ---
	// GetReferralCommissionList 获取佣金记录列表
	GetReferralCommissionList(ctx context.Context, req *v1.GetReferralCommissionListReq) (res *v1.GetReferralCommissionListRes, err error)

	// GetAvailableBalance 获取可用余额 (返回 big.Int 和小数位数)
	GetAvailableBalance(ctx context.Context, userId uint32, tokenId uint32) (*big.Int, uint8, error)

	// ListWallets 获取钱包列表
	ListWallets(ctx context.Context, req *v1.ListWalletsReq) (res *v1.ListWalletsRes, err error)

	// AdjustBalance 调整钱包余额
	AdjustBalance(ctx context.Context, req *v1.AdjustBalanceReq) (res *v1.AdjustBalanceRes, err error)

	// --- Merchant Admin API ---
	// GetMerchantList 获取商户列表
	GetMerchantList(ctx context.Context, req *v1.GetMerchantListReq) (res *v1.GetMerchantListRes, err error)

	// GetMerchant 获取商户详情
	GetMerchant(ctx context.Context, req *v1.GetMerchantReq) (res *v1.GetMerchantRes, err error)

	// AddMerchant 添加商户
	AddMerchant(ctx context.Context, req *v1.AddMerchantReq) (res *v1.AddMerchantRes, err error)

	// EditMerchant 编辑商户
	EditMerchant(ctx context.Context, req *v1.EditMerchantReq) (res *v1.EditMerchantRes, err error)

	// DeleteMerchant 删除商户
	DeleteMerchant(ctx context.Context, req *v1.DeleteMerchantReq) (res *v1.DeleteMerchantRes, err error)

	// UpdateMerchantStatus 更新商户状态
	UpdateMerchantStatus(ctx context.Context, req *v1.UpdateMerchantStatusReq) (res *v1.UpdateMerchantStatusRes, err error)

	// ResetMerchantGoogle2FA 重置商户Google 2FA
	ResetMerchantGoogle2FA(ctx context.Context, req *v1.ResetMerchantGoogle2FAReq) (res *v1.ResetMerchantGoogle2FARes, err error)

	// ResetMerchantPassword 重置商户密码
	ResetMerchantPassword(ctx context.Context, req *v1.ResetMerchantPasswordReq) (res *v1.ResetMerchantPasswordRes, err error)

	// GenerateMerchantApiKey 生成商户API密钥
	GenerateMerchantApiKey(ctx context.Context, req *v1.GenerateMerchantApiKeyReq) (res *v1.GenerateMerchantApiKeyRes, err error)

	// RevokeMerchantApiKey 撤销商户API密钥
	RevokeMerchantApiKey(ctx context.Context, req *v1.RevokeMerchantApiKeyReq) (res *v1.RevokeMerchantApiKeyRes, err error)

	// GetMerchantApiKeyList 获取商户API密钥列表
	GetMerchantApiKeyList(ctx context.Context, req *v1.GetMerchantApiKeyListReq) (res *v1.GetMerchantApiKeyListRes, err error)

	// UpdateMerchantApiKey 更新商户API密钥
	UpdateMerchantApiKey(ctx context.Context, req *v1.UpdateMerchantApiKeyReq) (res *v1.UpdateMerchantApiKeyRes, err error)
	// --- API Key Management ---
	// GetApiKeyList 获取API密钥列表
	GetApiKeyList(ctx context.Context, req *v1.GetApiKeyListReq) (res *v1.GetApiKeyListRes, err error)

	// UpdateApiKey 更新API密钥
	UpdateApiKey(ctx context.Context, req *v1.UpdateApiKeyReq) (res *v1.UpdateApiKeyRes, err error)

	// DeleteApiKey 删除API密钥
	DeleteApiKey(ctx context.Context, req *v1.DeleteApiKeyReq) (res *v1.DeleteApiKeyRes, err error)

	// UpdateApiKeyStatus 更新API密钥状态
	UpdateApiKeyStatus(ctx context.Context, req *v1.UpdateApiKeyStatusReq) (res *v1.UpdateApiKeyStatusRes, err error)

	// --- User Management ---
	// GetUserList 获取用户列表
	GetUserList(ctx context.Context, req *v1.GetUserListReq) (res *v1.GetUserListRes, err error)

	// GetUser 获取用户详情
	GetUser(ctx context.Context, req *v1.GetUserReq) (res *v1.GetUserRes, err error)

	// AddUser 添加用户
	AddUser(ctx context.Context, req *v1.AddUserReq) (res *v1.AddUserRes, err error)

	// EditUser 编辑用户
	EditUser(ctx context.Context, req *v1.EditUserReq) (res *v1.EditUserRes, err error)

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (res *v1.DeleteUserRes, err error)

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(ctx context.Context, req *v1.UpdateUserStatusReq) (res *v1.UpdateUserStatusRes, err error)

	// ResetUserPassword 重置用户密码
	ResetUserPassword(ctx context.Context, req *v1.ResetUserPasswordReq) (res *v1.ResetUserPasswordRes, err error)

	// ResetUserGoogle2FA 重置用户Google 2FA
	ResetUserGoogle2FA(ctx context.Context, req *v1.ResetUserGoogle2FAReq) (res *v1.ResetUserGoogle2FARes, err error)

	// --- User Backup Account Management ---
	// GetBackupAccounts 获取所有备用账户列表
	GetBackupAccounts(ctx context.Context, req *v1.GetBackupAccountsReq) (res *v1.GetBackupAccountsRes, err error)

	// GetUserBackupAccounts 获取用户备用账户列表
	GetUserBackupAccounts(ctx context.Context, req *v1.GetUserBackupAccountsReq) (res *v1.GetUserBackupAccountsRes, err error)

	// AddUserBackupAccount 添加用户备用账户
	AddUserBackupAccount(ctx context.Context, req *v1.AddUserBackupAccountReq) (res *v1.AddUserBackupAccountRes, err error)

	// DeleteUserBackupAccount 删除用户备用账户
	DeleteUserBackupAccount(ctx context.Context, req *v1.DeleteUserBackupAccountReq) (res *v1.DeleteUserBackupAccountRes, err error)

	// SetUserBackupAccountVerification 设置备用账户验证状态
	SetUserBackupAccountVerification(ctx context.Context, req *v1.SetUserBackupAccountVerificationReq) (res *v1.SetUserBackupAccountVerificationRes, err error)

	// --- Red Packet Admin API ---
	// ListAdminRedPackets 获取红包列表
	ListAdminRedPackets(ctx context.Context, req *v1.ListAdminRedPacketsReq) (res *v1.ListAdminRedPacketsRes, err error)
	// GetAdminRedPacketDetail 获取红包详情
	GetAdminRedPacketDetail(ctx context.Context, req *v1.GetAdminRedPacketDetailReq) (res *v1.GetAdminRedPacketDetailRes, err error)
	// CancelRedPacket 取消红包
	CancelRedPacket(ctx context.Context, req *v1.CancelRedPacketReq) (res *v1.CancelRedPacketRes, err error)
	// ListAdminRedPacketClaims 获取红包领取记录列表
	ListAdminRedPacketClaims(ctx context.Context, req *v1.ListAdminRedPacketClaimsReq) (res *v1.ListAdminRedPacketClaimsRes, err error)
	// ReviewRedPacketImage 审核红包封面图片
	ReviewRedPacketImage(ctx context.Context, req *v1.ReviewRedPacketImageReq) (res *v1.ReviewRedPacketImageRes, err error)
	// ListRedPacketImages 获取红包封面图片列表
	ListRedPacketImages(ctx context.Context, req *v1.ListRedPacketImagesReq) (res *v1.ListRedPacketImagesRes, err error)

	// ListAdminTransfers 获取转账记录列表
	ListAdminTransfers(ctx context.Context, req *v1.ListAdminTransfersReq) (res *v1.ListAdminTransfersRes, err error)

	// GetAdminTransferDetail 获取转账记录详情
	GetAdminTransferDetail(ctx context.Context, req *v1.GetAdminTransferDetailReq) (res *v1.GetAdminTransferDetailRes, err error)

	// --- Payment Request Admin API ---
	// ListPaymentRequests 获取收款请求列表
	ListPaymentRequests(ctx context.Context, req *v1.ListPaymentRequestReq) (res *v1.ListPaymentRequestRes, err error)
	// GetPaymentRequestDetail 获取收款请求详情
	GetPaymentRequestDetail(ctx context.Context, req *v1.GetPaymentRequestDetailReq) (res *v1.GetPaymentRequestDetailRes, err error)
	// UpdatePaymentRequestStatus 更新收款请求状态
	UpdatePaymentRequestStatus(ctx context.Context, req *v1.UpdatePaymentRequestStatusReq) (res *v1.UpdatePaymentRequestStatusRes, err error)

	//配置项列表

	// ListCategories retrieves a paginated list of config categories based on criteria.
	ListConfigCategory(ctx context.Context, req *v1.ListConfigCategoryReq) (res *v1.ListConfigCategoryRes, err error) // Renamed from List

	// CreateCategory adds a new config category.
	CreateConfigCategory(ctx context.Context, req *v1.CreateConfigCategoryReq) (res *v1.CreateConfigCategoryRes, err error) // Renamed from Create

	// UpdateCategory modifies an existing config category.
	UpdateConfigCategory(ctx context.Context, req *v1.UpdateConfigCategoryReq) (res *v1.UpdateConfigCategoryRes, err error) // Renamed from Update

	// DeleteCategories removes one or more config categories by their IDs.
	DeleteConfigCategory(ctx context.Context, req *v1.DeleteConfigCategoryReq) (res *v1.DeleteConfigCategoryRes, err error) // Renamed from Delete

	// ListConfigItem retrieves a paginated list of config items based on criteria.
	ListConfigItem(ctx context.Context, req *v1.ListConfigItemReq) (res *v1.ListConfigItemRes, err error)

	// CreateItem adds a new config item under a specific category.
	CreateConfigItem(ctx context.Context, req *v1.CreateConfigItemReq) (res *v1.CreateConfigItemRes, err error) // Renamed from Create

	// UpdateItem modifies an existing config item.
	UpdateConfigItem(ctx context.Context, req *v1.UpdateConfigItemReq) (res *v1.UpdateConfigItemRes, err error) // Renamed from Update

	// DeleteConfigItem removes one or more config items by their IDs.
	DeleteConfigItem(ctx context.Context, req *v1.DeleteConfigItemReq) (res *v1.DeleteConfigItemRes, err error)
	// --- Permission Management ---
	GetPermissionList(ctx context.Context, req *v1.GetPermissionListReq) (res *v1.GetPermissionListRes, err error)
	GetAllPermissionList(ctx context.Context, req *v1.GetAllPermissionListReq) (res *v1.GetAllPermissionListRes, err error) // 添加 GetAll
	AddPermission(ctx context.Context, tx gdb.TX, req *v1.AddPermissionReq) (res *v1.AddPermissionRes, err error)
	EditPermission(ctx context.Context, req *v1.EditPermissionReq) (res *v1.EditPermissionRes, err error)
	DeletePermission(ctx context.Context, req *v1.DeletePermissionReq) (res *v1.DeletePermissionRes, err error)

	// GetUserAccessibleMenus 获取当前用户可访问的菜单树
	GetUserAccessibleMenus(ctx context.Context, req *v1.GetUserAccessibleMenusReq) (res *v1.GetUserAccessibleMenusRes, err error)

	// --- Admin Member Role Management ---
	// AssignRolesToAdminMember 分配角色给管理员用户
	AssignRolesToAdminMember(ctx context.Context, req *v1.AssignRolesToAdminMemberReq) (res *v1.AssignRolesToAdminMemberRes, err error)
	// GetAdminMemberAssignedRoles 获取管理员用户已分配的角色
	GetAdminMemberAssignedRoles(ctx context.Context, req *v1.GetAdminMemberAssignedRolesReq) (res *v1.GetAdminMemberAssignedRolesRes, err error)

	// --- Menu Permissions Sync ---
	// SyncMenuPermissions 同步菜单到权限表
	SyncMenuPermissions(ctx context.Context, req *v1.SyncMenuPermissionsReq) (res *v1.SyncMenuPermissionsRes, err error)

	// PermissionExistsByKey checks if a permission with the given key already exists.
	PermissionExistsByKey(ctx context.Context, key string) (bool, error)

	// --- API Permissions Sync ---
	// SyncApiPermissions 同步API到权限表
	SyncApiPermissions(ctx context.Context, req *v1.SyncApiPermissionsReq) (res *v1.SyncApiPermissionsRes, err error)
	// CasdoorSignin Casdoor 单点登录
	CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error)

	// GetTokenSymbols 获取代币符号列表 (用于搜索条件下拉框)
	GetTokenSymbols(ctx context.Context, req *v1.GetTokenSymbolsReq) (res *v1.GetTokenSymbolsRes, err error)

	// GetCasdoorUserInfo 获取 Casdoor 用户信息
	GetCasdoorUserInfo(ctx context.Context, req *v1.GetCasdoorUserInfoReq) (res *v1.GetCasdoorUserInfoRes, err error)

	// ListUserRecharges 获取用户充值记录
	ListUserRecharges(ctx context.Context, req *v1.ListUserRechargesReq) (res *v1.ListUserRechargesRes, err error)

	// ListUserAddresses 获取用户充值地址列表
	ListUserAddresses(ctx context.Context, req *v1.ListUserAddressesReq) (res *v1.ListUserAddressesRes, err error)

	// GetAddressStatistics 获取地址统计信息
	GetAddressStatistics(ctx context.Context, req *v1.GetAddressStatisticsReq) (res *v1.GetAddressStatisticsRes, err error)

	// --- User Withdraws API ---
	// ListUserWithdraws 获取提现记录列表
	ListUserWithdraws(ctx context.Context, req *v1.ListUserWithdrawsReq) (res *v1.ListUserWithdrawsRes, err error)
	// GetUserWithdrawDetail 获取提现记录详情
	GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error)
	// ReviewUserWithdraw 审核提现记录
	ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error)
	// UpdateUserWithdrawStatus 更新提现记录状态
	UpdateUserWithdrawStatus(ctx context.Context, req *v1.UpdateUserWithdrawStatusReq) (res *v1.UpdateUserWithdrawStatusRes, err error)
	// ApproveUserWithdraw 审批用户提现申请
	ApproveUserWithdraw(ctx context.Context, req *v1.ApproveUserWithdrawReq) (res *v1.ApproveUserWithdrawRes, err error)
	// RejectUserWithdraw 拒绝用户提现申请
	RejectUserWithdraw(ctx context.Context, req *v1.RejectUserWithdrawReq) (res *v1.RejectUserWithdrawRes, err error)
	// UpdateTransferResult 更新转账结果
	UpdateTransferResult(ctx context.Context, req *v1.UpdateTransferResultReq) (res *v1.UpdateTransferResultRes, err error)
	// GetPendingAudits 获取待审核提现记录
	GetPendingAudits(ctx context.Context, req *v1.GetPendingAuditsReq) (res *v1.GetPendingAuditsRes, err error)

	// ImportAddresses 导入地址数据
	ImportAddresses(ctx context.Context, req *v1.ImportAddressesReq) (res *v1.ImportAddressesRes, err error)

	// GetImportProgress 获取导入进度
	GetImportProgress(ctx context.Context, req *v1.GetImportProgressReq) (res *v1.GetImportProgressRes, err error)

	// GetDashboardStats 获取管理后台首页统计信息
	GetDashboardStats(ctx context.Context, req *v1.GetDashboardStatsReq) (res *v1.GetDashboardStatsRes, err error)

	// --- Merchant My API (商户自管理接口) ---
	// 商户回调记录接口
	GetMyCallbacks(ctx context.Context, req *v1.GetMyCallbacksReq) (res *v1.GetMyCallbacksRes, err error)
	GetMyCallbackDetail(ctx context.Context, req *v1.GetMyCallbackDetailReq) (res *v1.GetMyCallbackDetailRes, err error)
	RetryCallback(ctx context.Context, req *v1.RetryCallbackReq) (res *v1.RetryCallbackRes, err error)

	// 商户充值记录接口
	GetMyDeposits(ctx context.Context, req *v1.GetMyDepositsReq) (res *v1.GetMyDepositsRes, err error)
	GetMyDepositDetail(ctx context.Context, req *v1.GetMyDepositDetailReq) (res *v1.GetMyDepositDetailRes, err error)

	// 商户资金记录接口
	GetMyTransactions(ctx context.Context, req *v1.GetMyTransactionsReq) (res *v1.GetMyTransactionsRes, err error)
	GetMyTransactionDetail(ctx context.Context, req *v1.GetMyTransactionDetailReq) (res *v1.GetMyTransactionDetailRes, err error)
	GetMyTransactionStats(ctx context.Context, req *v1.GetMyTransactionStatsReq) (res *v1.GetMyTransactionStatsRes, err error)

	// 商户提现记录接口
	GetMyWithdraws(ctx context.Context, req *v1.GetMyWithdrawsReq) (res *v1.GetMyWithdrawsRes, err error)
	GetMyWithdrawDetail(ctx context.Context, req *v1.GetMyWithdrawDetailReq) (res *v1.GetMyWithdrawDetailRes, err error)
	CancelMyWithdraw(ctx context.Context, req *v1.CancelMyWithdrawReq) (res *v1.CancelMyWithdrawRes, err error)
	CreateMyWithdraw(ctx context.Context, req *v1.CreateMyWithdrawReq) (res *v1.CreateMyWithdrawRes, err error)
	GetMyWithdrawFee(ctx context.Context, req *v1.GetMyWithdrawFeeReq) (res *v1.GetMyWithdrawFeeRes, err error)

	// 管理员提现审批接口
	ApproveWithdraw(ctx context.Context, req *v1.ApproveWithdrawReq) (res *v1.ApproveWithdrawRes, err error)
	RejectWithdraw(ctx context.Context, req *v1.RejectWithdrawReq) (res *v1.RejectWithdrawRes, err error)

	// --- 商户资产管理接口 ---
	// 获取商户资产概览
	GetMerchantAssets(ctx context.Context, req *v1.GetMerchantAssetsReq) (res *v1.GetMerchantAssetsRes, err error)
	// 获取商户钱包列表
	GetMerchantWallets(ctx context.Context, req *v1.GetMerchantWalletsReq) (res *v1.GetMerchantWalletsRes, err error)
	// 调整商户余额
	AdjustMerchantBalance(ctx context.Context, req *v1.AdjustMerchantBalanceReq) (res *v1.AdjustMerchantBalanceRes, err error)
	// 获取商户钱包详情
	GetMerchantWalletDetail(ctx context.Context, req *v1.GetMerchantWalletDetailReq) (res *v1.GetMerchantWalletDetailRes, err error)

	// --- 提现配置管理接口 ---
	// 提现金额设置
	ListWithdrawalAmountSettings(ctx context.Context, req *v1.ListWithdrawalAmountSettingsReq) (res *v1.ListWithdrawalAmountSettingsRes, err error)
	CreateWithdrawalAmountSetting(ctx context.Context, req *v1.CreateWithdrawalAmountSettingReq) (res *v1.CreateWithdrawalAmountSettingRes, err error)
	UpdateWithdrawalAmountSetting(ctx context.Context, req *v1.UpdateWithdrawalAmountSettingReq) (res *v1.UpdateWithdrawalAmountSettingRes, err error)
	DeleteWithdrawalAmountSetting(ctx context.Context, req *v1.DeleteWithdrawalAmountSettingReq) (res *v1.DeleteWithdrawalAmountSettingRes, err error)

	// 提现审核设置
	ListWithdrawalApprovalSettings(ctx context.Context, req *v1.ListWithdrawalApprovalSettingsReq) (res *v1.ListWithdrawalApprovalSettingsRes, err error)
	CreateWithdrawalApprovalSetting(ctx context.Context, req *v1.CreateWithdrawalApprovalSettingReq) (res *v1.CreateWithdrawalApprovalSettingRes, err error)
	UpdateWithdrawalApprovalSetting(ctx context.Context, req *v1.UpdateWithdrawalApprovalSettingReq) (res *v1.UpdateWithdrawalApprovalSettingRes, err error)
	DeleteWithdrawalApprovalSetting(ctx context.Context, req *v1.DeleteWithdrawalApprovalSettingReq) (res *v1.DeleteWithdrawalApprovalSettingRes, err error)

	// 提现手续费设置
	ListWithdrawalFeeSettings(ctx context.Context, req *v1.ListWithdrawalFeeSettingsReq) (res *v1.ListWithdrawalFeeSettingsRes, err error)
	CreateWithdrawalFeeSetting(ctx context.Context, req *v1.CreateWithdrawalFeeSettingReq) (res *v1.CreateWithdrawalFeeSettingRes, err error)
	UpdateWithdrawalFeeSetting(ctx context.Context, req *v1.UpdateWithdrawalFeeSettingReq) (res *v1.UpdateWithdrawalFeeSettingRes, err error)
	DeleteWithdrawalFeeSetting(ctx context.Context, req *v1.DeleteWithdrawalFeeSettingReq) (res *v1.DeleteWithdrawalFeeSettingRes, err error)

	// 提现风控设置
	ListWithdrawalRiskControlSettings(ctx context.Context, req *v1.ListWithdrawalRiskControlSettingsReq) (res *v1.ListWithdrawalRiskControlSettingsRes, err error)
	CreateWithdrawalRiskControlSetting(ctx context.Context, req *v1.CreateWithdrawalRiskControlSettingReq) (res *v1.CreateWithdrawalRiskControlSettingRes, err error)
	UpdateWithdrawalRiskControlSetting(ctx context.Context, req *v1.UpdateWithdrawalRiskControlSettingReq) (res *v1.UpdateWithdrawalRiskControlSettingRes, err error)
	DeleteWithdrawalRiskControlSetting(ctx context.Context, req *v1.DeleteWithdrawalRiskControlSettingReq) (res *v1.DeleteWithdrawalRiskControlSettingRes, err error)
}
