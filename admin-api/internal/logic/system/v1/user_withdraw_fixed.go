package v1

import (
	"context"
	"fmt"

	"admin-api/api/common"
	v1 "admin-api/api/system/v1"
	"admin-api/internal/codes"
	adminConstants "admin-api/internal/constants"
	"admin-api/internal/utils"

	"github.com/yalks/wallet"
	"github.com/yalks/wallet/constants"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ListUserWithdraws 获取提现记录列表（带代理和telegram信息）
func (s *sSystemLogic) ListUserWithdraws(ctx context.Context, req *v1.ListUserWithdrawsReq) (res *v1.ListUserWithdrawsRes, err error) {
	// 初始化返回结果
	res = &v1.ListUserWithdrawsRes{
		Page: common.PageResponse{
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		},
		Data: make([]*v1.UserWithdrawsListItem, 0),
	}

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户提现记录列表（带代理和telegram信息）
	list, total, err := s.userWithdrawRepo.ListWithAgentInfo(ctx, req)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取用户提现记录列表失败")
	}

	// 设置分页信息
	res.Page.TotalSize = total
	res.Page.TotalPage = common.CalculateTotalPage(total, req.PageSize)

	// 设置返回数据
	res.Data = list

	return res, nil
}

// GetUserWithdrawDetail 获取提现记录详情
func (s *sSystemLogic) GetUserWithdrawDetail(ctx context.Context, req *v1.GetUserWithdrawDetailReq) (res *v1.GetUserWithdrawDetailRes, err error) {
	// 初始化返回结果
	res = &v1.GetUserWithdrawDetailRes{
		Data: nil,
	}

	// 查询数据（带代理和telegram信息）
	detail, err := s.userWithdrawRepo.GetDetailWithAgentInfo(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if detail == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 直接使用返回的数据（已包含代理和telegram信息）
	res.Data = detail

	return res, nil
}

// ReviewUserWithdraw 审核提现记录
func (s *sSystemLogic) ReviewUserWithdraw(ctx context.Context, req *v1.ReviewUserWithdrawReq) (res *v1.ReviewUserWithdrawRes, err error) {
	// 初始化返回结果
	res = &v1.ReviewUserWithdrawRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查审核状态是否为待审核
	if withdraw.AuditStatus != 2 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只能审核待审核状态的提现记录")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var updateErr error

		// 根据操作类型更新状态
		if req.Action == "approve" {
			// 通过: 审核通过(3) + 成功(4)
			updateErr = s.userWithdrawRepo.UpdateStatusWithReason(ctx, tx, req.UserWithdrawsId, 3, 4, "", "", req.AdminRemark)
		} else if req.Action == "reject" {
			// 拒绝: 审核拒绝(4) + 无处理状态(0)
			updateErr = s.userWithdrawRepo.UpdateStatusWithReason(ctx, tx, req.UserWithdrawsId, 4, 0, req.RefuseReasonZh, req.RefuseReasonEn, req.AdminRemark)

			// 获取代币信息以获取符号 (Symbol) 和 精度 (Decimals)
			token, err := s.tokenRepo.GetByID(ctx, uint(withdraw.TokenId))
			if err != nil {
				return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", withdraw.TokenId)
			}
			if token == nil {
				return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", withdraw.TokenId)
			}
			symbol := token.Symbol

			// --- 事务开始 ---
			// 获取管理员名称
			adminName := ""
			if r := g.RequestFromCtx(ctx); r == nil {
				return codes.NewError(codes.CodeUnauthorized)
			} else {
				adminName = r.GetCtxVar("username").String()
			}

			// 使用新的资金操作描述器
			descriptor := utils.NewFundOperationDescriptor("zh")
			businessID := descriptor.GenerateBusinessID(adminConstants.FundOpWithdrawRefund, withdraw.UserId, gtime.Now().Unix())

			// 构建描述信息
			memo := fmt.Sprintf("%s-%s", adminName, req.AdminRemark)
			description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpWithdrawRefund, withdraw.Amount.String(), symbol, memo)

			// 使用新的请求构建方法
			walletReq := &constants.FundOperationRequest{
				UserID:      uint64(withdraw.UserId),
				TokenSymbol: symbol,
				Amount:      withdraw.Amount,
				BusinessID:  businessID,
				FundType:    constants.FundTypeWithdrawRefund,
				Description: description,
				Metadata: map[string]string{
					"type":          "withdraw_refund",
					"request_id":    fmt.Sprintf("%d", withdraw.UserId),
					"operation":     "withdraw_refund",
					"changeReason":  req.AdminRemark,
					"admin_name":    adminName,
					"withdraw_id":   fmt.Sprintf("%d", withdraw.UserWithdrawsId),
					"refuse_reason": req.RefuseReasonZh,
				},
				RequestSource: "admin",
			}

			_, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
			if err != nil {
				g.Log().Errorf(ctx, "退还用户资金失败: %v", err)
				return gerror.Wrap(err, "退还用户资金失败")
			}

		}

		if updateErr != nil {
			return updateErr
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// UpdateUserWithdrawStatus 更新提现记录状态
func (s *sSystemLogic) UpdateUserWithdrawStatus(ctx context.Context, req *v1.UpdateUserWithdrawStatusReq) (res *v1.UpdateUserWithdrawStatusRes, err error) {
	// 初始化返回结果
	res = &v1.UpdateUserWithdrawStatusRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查状态转换是否合法 - 基于实际的审核状态和处理状态
	if !isValidStatusTransition(withdraw.AuditStatus, withdraw.ProcessingStatus, req.AuditStatus, req.ProcessingStatus) {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "无效的状态转换")
	}

	// 使用事务处理
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 直接更新审核状态和处理状态
		return s.userWithdrawRepo.UpdateStatusWithTxInfo(ctx, tx, req.UserWithdrawsId, req.AuditStatus, req.ProcessingStatus, req.TxHash, req.ErrorMessage, req.AdminRemark)
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// isValidStatusTransition 检查状态转换是否合法
func isValidStatusTransition(currentAuditStatus, currentProcessingStatus, targetAuditStatus, targetProcessingStatus uint) bool {
	// 审核状态转换规则:
	// 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
	// 处理状态转换规则:
	// 0-无, 1-自动放币处理中, 2-处理中, 3-待人工转账, 4-成功, 5-失败

	// 允许的状态转换:
	// 1. 审核通过(3) + 处理中(1,2,3) -> 审核通过(3) + 成功(4)
	// 2. 审核通过(3) + 处理中(1,2,3) -> 审核通过(3) + 失败(5)
	// 3. 管理员可以手动设置为处理中状态 (任何状态 -> 审核通过(3) + 处理中(1,2,3))
	// 4. 允许重新设置为相同状态 (用于更新其他字段如txHash, errorMessage等)

	// 允许重新设置为相同状态
	if currentAuditStatus == targetAuditStatus && currentProcessingStatus == targetProcessingStatus {
		return true
	}

	// 如果当前是审核通过且在处理中，允许转换为成功或失败
	if currentAuditStatus == 3 && (currentProcessingStatus == 1 || currentProcessingStatus == 2 || currentProcessingStatus == 3) {
		if targetAuditStatus == 3 && (targetProcessingStatus == 4 || targetProcessingStatus == 5) {
			return true
		}
	}

	// 管理员手动干预：允许设置为处理中状态
	if targetAuditStatus == 3 && (targetProcessingStatus == 1 || targetProcessingStatus == 2 || targetProcessingStatus == 3) {
		return true
	}

	// 允许从任何状态设置为失败状态（用于异常处理）
	if targetAuditStatus == 3 && targetProcessingStatus == 5 {
		return true
	}

	return false
}

// ApproveUserWithdraw 审批用户提现申请
func (s *sSystemLogic) ApproveUserWithdraw(ctx context.Context, req *v1.ApproveUserWithdrawReq) (res *v1.ApproveUserWithdrawRes, err error) {
	// 初始化返回结果
	res = &v1.ApproveUserWithdrawRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查审核状态是否为待审核(2)
	if withdraw.AuditStatus != 2 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只有待审核状态的提现才能审批")
	}

	// 开始事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 根据提现金额和配置确定处理方式
		// amount := withdraw.Amount

		// 查询该币种和网络的审批配置
		var approvalSetting struct {
			ApprovalAutoMin   float64 `json:"approval_auto_min"`
			ApprovalAutoMax   float64 `json:"approval_auto_max"`
			ApprovalManualMin float64 `json:"approval_manual_min"`
			ApprovalManualMax float64 `json:"approval_manual_max"`
		}

		// 获取币种信息
		var token struct {
			Symbol string `json:"symbol"`
		}
		err = tx.Model("tokens").Where("token_id", withdraw.TokenId).Scan(&token)
		if err != nil {
			return gerror.Wrap(err, "获取币种信息失败")
		}

		// 查询审批配置
		err = tx.Model("withdrawal_approval_settings").
			Where("currency", token.Symbol).
			Where("network", withdraw.Chan).
			Where("status", 1).
			Scan(&approvalSetting)
		if err != nil {
			// 如果是没有找到记录的错误，返回更友好的提示
			if err.Error() == "sql: no rows in result set" {
				return gerror.NewCode(codes.CodeNotFound,
					fmt.Sprintf("未找到币种[%s]网络[%s]的提现审批配置，请先配置审批设置",
						token.Symbol, withdraw.Chan))
			}
			return gerror.Wrap(err, "查询审批配置失败")
		}

		// 检查是否查询到审批配置（通过判断是否有值）
		if approvalSetting.ApprovalAutoMax == 0 && approvalSetting.ApprovalManualMax == 0 {
			return gerror.NewCode(codes.CodeNotFound,
				fmt.Sprintf("未找到币种[%s]网络[%s]的提现审批配置，请先配置审批设置",
					token.Symbol, withdraw.Chan))
		}

		// 使用实际到账金额进行判断
		actualAmountFloat, _ := withdraw.ActualAmount.Float64()

		// 判断金额所在区间并设置处理状态
		var processingStatus uint

		// 1. 检查是否在自动放币区间
		if actualAmountFloat >= approvalSetting.ApprovalAutoMin && actualAmountFloat <= approvalSetting.ApprovalAutoMax {
			processingStatus = 1 // 自动放币处理中
		} else if actualAmountFloat >= approvalSetting.ApprovalManualMin &&
			(approvalSetting.ApprovalManualMax == 0 || actualAmountFloat <= approvalSetting.ApprovalManualMax) {
			// 2. 检查是否在手动放币区间（注意：approval_manual_max 为 0 表示无限大）
			processingStatus = 3 // 待人工转账
		} else {
			// 3. 金额不在任何有效区间内
			return gerror.NewCode(codes.CodeInvalidParameter,
				fmt.Sprintf("提现实际到账金额 %.8f %s 不在有效的审批区间内，请检查审批配置",
					actualAmountFloat, token.Symbol))
		}

		updateData := g.Map{
			"audit_status":      3, // 审核通过
			"processing_status": processingStatus,
			"admin_remark":      req.AdminRemark,
			"checked_at":        gtime.Now(),
			"updated_at":        gtime.Now(),
		}

		// 更新提现记录
		_, err = tx.Model("user_withdraws").
			Where("user_withdraws_id", req.UserWithdrawsId).
			Data(updateData).
			Update()
		if err != nil {
			return gerror.Wrap(err, "更新提现记录失败")
		}

		// 记录操作日志
		g.Log().Infof(ctx, "用户提现审批: 审批通过用户提现申请，ID: %d", req.UserWithdrawsId)

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// RejectUserWithdraw 拒绝用户提现申请
func (s *sSystemLogic) RejectUserWithdraw(ctx context.Context, req *v1.RejectUserWithdrawReq) (res *v1.RejectUserWithdrawRes, err error) {
	// 初始化返回结果
	res = &v1.RejectUserWithdrawRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 添加调试日志
	g.Log().Infof(ctx, "RejectUserWithdraw: 提现记录ID=%d, AuditStatus=%d, ProcessingStatus=%d",
		req.UserWithdrawsId, withdraw.AuditStatus, withdraw.ProcessingStatus)

	// 检查审核状态是否为待审核(2)
	if withdraw.AuditStatus != 2 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter,
			fmt.Sprintf("只有待审核状态的提现才能拒绝，当前状态: %d", withdraw.AuditStatus))
	}

	// 开始事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新提现记录
		updateData := g.Map{
			"audit_status":      4, // 审核拒绝
			"processing_status": 0, // 无处理状态
			"refuse_reason_zh":  req.RefuseReason,
			"refuse_reason_en":  req.RefuseReason, // 暂时使用相同的拒绝原因
			"admin_remark":      req.AdminRemark,
			"checked_at":        gtime.Now(),
			"updated_at":        gtime.Now(),
		}

		_, err = tx.Model("user_withdraws").
			Where("user_withdraws_id", req.UserWithdrawsId).
			Data(updateData).
			Update()
		if err != nil {
			return gerror.Wrap(err, "更新提现记录失败")
		}

		// 退还用户资金
		// 获取代币信息以获取符号 (Symbol)
		token, err := s.tokenRepo.GetByID(ctx, uint(withdraw.TokenId))
		if err != nil {
			return gerror.Wrapf(err, "获取代币信息失败 (tokenId: %d)", withdraw.TokenId)
		}
		if token == nil {
			return gerror.NewCodef(codes.CodeTokenNotFound, "代币不存在 (tokenId: %d)", withdraw.TokenId)
		}
		symbol := token.Symbol

		// 获取管理员名称
		adminName := ""
		if r := g.RequestFromCtx(ctx); r == nil {
			return codes.NewError(codes.CodeUnauthorized)
		} else {
			adminName = r.GetCtxVar("username").String()
		}

		// 使用新的资金操作描述器
		descriptor := utils.NewFundOperationDescriptor("zh")
		businessID := descriptor.GenerateBusinessID(adminConstants.FundOpWithdrawRefund, withdraw.UserId, gtime.Now().Unix())

		// 构建描述信息
		memo := fmt.Sprintf("%s-%s", adminName, req.AdminRemark)
		description := descriptor.FormatDescriptionWithMemo(adminConstants.FundOpWithdrawRefund, withdraw.Amount.String(), symbol, memo)

		// 使用新的请求构建方法
		walletReq := &constants.FundOperationRequest{
			UserID:      uint64(withdraw.UserId),
			TokenSymbol: symbol,
			Amount:      withdraw.Amount,
			BusinessID:  businessID,
			FundType:    constants.FundTypeWithdrawRefund,
			Description: description,
			Metadata: map[string]string{
				"type":          "withdraw_refund",
				"request_id":    fmt.Sprintf("%d", withdraw.UserId),
				"operation":     "withdraw_refund",
				"changeReason":  req.AdminRemark,
				"admin_name":    adminName,
				"withdraw_id":   fmt.Sprintf("%d", withdraw.UserWithdrawsId),
				"refuse_reason": req.RefuseReason,
			},
			RequestSource: "admin",
		}

		_, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, walletReq)
		if err != nil {
			g.Log().Errorf(ctx, "退还用户资金失败: %v", err)
			return gerror.Wrap(err, "退还用户资金失败")
		}

		// 记录操作日志
		g.Log().Infof(ctx, "用户提现审批: 拒绝用户提现申请，ID: %d, 原因: %s", req.UserWithdrawsId, req.RefuseReason)

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// UpdateTransferResult 更新转账结果
func (s *sSystemLogic) UpdateTransferResult(ctx context.Context, req *v1.UpdateTransferResultReq) (res *v1.UpdateTransferResultRes, err error) {
	// 初始化返回结果
	res = &v1.UpdateTransferResultRes{
		Success: false,
	}

	// 查询提现记录
	withdraw, err := s.userWithdrawRepo.GetByID(ctx, req.UserWithdrawsId)
	if err != nil {
		return nil, err
	}

	// 检查是否存在
	if withdraw == nil {
		return nil, gerror.NewCode(codes.CodeNotFound, "提现记录不存在")
	}

	// 检查处理状态是否为待人工转账(3)
	if withdraw.ProcessingStatus != 3 {
		return nil, gerror.NewCode(codes.CodeInvalidParameter, "只有待人工转账状态的提现才能更新转账结果")
	}

	// 开始事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var processingStatus uint
		var auditStatus uint
		if req.Result == "success" {
			processingStatus = 4 // 成功
			// 更新审核状态为审核通过(3)
			auditStatus = 3
		} else {
			processingStatus = 5 // 失败
			// 更新审核状态为审核拒绝(4)
			auditStatus = 4
		}

		// 更新提现记录
		updateData := g.Map{
			"processing_status": processingStatus,
			"tx_hash":           req.TxHash,
			"refuse_reason_zh":  req.ErrorMessage,
			"refuse_reason_en":  req.ErrorMessage,

			"error_message": req.ErrorMessage,
			"audit_status":  auditStatus,
			// "admin_remark":      req.AdminRemark,
			"completed_at": gtime.Now(),
			"updated_at":   gtime.Now(),
		}

		_, err = tx.Model("user_withdraws").
			Where("user_withdraws_id", req.UserWithdrawsId).
			Data(updateData).
			Update()
		if err != nil {
			return gerror.Wrap(err, "更新提现记录失败")
		}

		// 记录操作日志
		g.Log().Infof(ctx, "更新转账结果: 更新用户提现转账结果，ID: %d, 结果: %s", req.UserWithdrawsId, req.Result)

		return nil
	})

	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// GetPendingAudits 获取待审核提现记录
func (s *sSystemLogic) GetPendingAudits(ctx context.Context, req *v1.GetPendingAuditsReq) (res *v1.GetPendingAuditsRes, err error) {
	// 初始化返回结果
	res = &v1.GetPendingAuditsRes{
		Count:       0,
		Withdraws:   make([]*v1.PendingWithdrawInfo, 0),
		HasNewItems: false,
	}

	// 缓存键
	cacheKey := "pending_audit_withdrawals"
	
	// 尝试从缓存获取数据 (500ms TTL)
	cachedData, err := g.Redis().Get(ctx, cacheKey)
	if err == nil && !cachedData.IsNil() && !cachedData.IsEmpty() {
		err = cachedData.Scan(&res)
		if err == nil {
			return res, nil
		}
		// 缓存解析失败，继续从数据库查询
		g.Log().Warningf(ctx, "Failed to parse cached pending audits: %v", err)
	}

	// 构建查询条件 - 只查询 audit_status = 2 (待审核) 的记录
	listReq := &v1.ListUserWithdrawsReq{
		PageRequest: common.PageRequest{
			Page:     1,
			PageSize: 100, // 限制最多返回100条待审核记录
		},
		AuditStatus: &[]uint{2}[0], // 待审核状态
	}

	// 查询待审核的提现记录
	list, total, err := s.userWithdrawRepo.ListWithAgentInfo(ctx, listReq)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeInternalError, err, "获取待审核提现记录失败")
	}

	// 设置返回数据
	res.Count = total

	// 转换数据格式
	for _, item := range list {
		// 计算等待时间
		waitingTime := ""
		if item.CreatedAt != nil {
			duration := gtime.Now().Sub(item.CreatedAt)
			hours := int(duration.Hours())
			minutes := int(duration.Minutes()) % 60
			
			if hours > 0 {
				waitingTime = fmt.Sprintf("%d小时%d分钟", hours, minutes)
			} else {
				waitingTime = fmt.Sprintf("%d分钟", minutes)
			}
		}

		pendingInfo := &v1.PendingWithdrawInfo{
			UserWithdrawsId: item.UserWithdrawsId,
			UserId:          item.UserId,
			Account:         item.Account,
			Nickname:        item.Nickname,
			Symbol:          item.Symbol,
			Amount:          item.Amount,
			ActualAmount:    item.ActualAmount,
			CreatedAt:       item.CreatedAt,
			WaitingTime:     waitingTime,
			OrderNo:         item.OrderNo,
			Address:         item.Address,
			Chain:           item.Chain,
		}

		res.Withdraws = append(res.Withdraws, pendingInfo)
	}

	// 检查是否有新记录（可以基于客户端传来的最后检查时间来判断）
	// 这里简单处理，如果有待审核记录就认为有新记录
	res.HasNewItems = total > 0

	// 将结果存入缓存，设置500ms过期时间
	if total > 0 || len(res.Withdraws) > 0 {
		err = g.Redis().SetEX(ctx, cacheKey, res, 500) // 500ms TTL
		if err != nil {
			// 缓存失败不影响主流程，仅记录日志
			g.Log().Warningf(ctx, "Failed to cache pending audits: %v", err)
		}
	}

	return res, nil
}
